-- =====================================================
-- DELIVERY ORDER MODULE - DATABASE TABLES
-- =====================================================
-- Database: toko_elektronik
-- Created: 2025-01-15
-- Description: Complete delivery order management tables
-- =====================================================

USE toko_elektronik;

-- Drop tables if exist (for clean installation)
DROP TABLE IF EXISTS `delivery_order_detail`;
DROP TABLE IF EXISTS `delivery_orders`;

-- =====================================================
-- TABLE: delivery_orders (Header Table)
-- =====================================================
CREATE TABLE `delivery_orders` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `delivery_no` varchar(255) NOT NULL,
  `delivery_date` date NOT NULL,
  `delivery_notes` text,
  `order_id` bigint(20) NULL,
  `order_no` varchar(255) NULL,
  `customer_name` varchar(255) NULL,
  `customer_phone` varchar(20) NULL,
  `customer_email` varchar(100) NULL,
  `customer_address` text NULL,
  `delivery_status` enum('draft', 'pending', 'in_transit', 'delivered', 'cancelled', 'returned') DEFAULT 'draft',
  `priority` enum('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
  `scheduled_date` date NULL,
  `actual_delivery_date` datetime NULL,
  `driver_name` varchar(255) NULL,
  `driver_phone` varchar(20) NULL,
  `vehicle_info` varchar(255) NULL,
  `tracking_number` varchar(100) NULL,
  `delivery_method` enum('pickup', 'courier', 'express', 'standard') DEFAULT 'standard',
  `shipping_cost` decimal(15,2) DEFAULT 0.00,
  `total_items` int(11) DEFAULT 0,
  `total_qty` decimal(10,2) DEFAULT 0.00,
  `total_weight` decimal(10,2) DEFAULT 0.00,
  `delivery_instructions` text NULL,
  `recipient_name` varchar(255) NULL,
  `recipient_phone` varchar(20) NULL,
  `signature_required` enum('yes', 'no') DEFAULT 'no',
  `proof_of_delivery` varchar(255) NULL,
  `delivery_fee` decimal(15,2) DEFAULT 0.00,
  `insurance_fee` decimal(15,2) DEFAULT 0.00,
  `total_amount` decimal(15,2) DEFAULT 0.00,
  `created_by` int(11) NULL,
  `updated_by` int(11) NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `approved_by` int(11) NULL,
  `approved_at` timestamp NULL,
  `cancelled_reason` text NULL,
  `internal_notes` text NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `delivery_no` (`delivery_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_delivery_status` (`delivery_status`),
  KEY `idx_delivery_date` (`delivery_date`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_updated_by` (`updated_by`),
  KEY `idx_approved_by` (`approved_by`),
  KEY `idx_customer_name` (`customer_name`),
  KEY `idx_tracking_number` (`tracking_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- =====================================================
-- TABLE: delivery_order_detail (Detail Table)
-- =====================================================
CREATE TABLE `delivery_order_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `delivery_order_id` bigint(20) NOT NULL,
  `order_detail_id` bigint(20) NULL,
  `id_barang` int(11) NOT NULL,
  `qty_ordered` decimal(10,2) DEFAULT 0.00,
  `qty_delivered` decimal(10,2) NOT NULL DEFAULT 0.00,
  `qty_remaining` decimal(10,2) DEFAULT 0.00,
  `unit_weight` decimal(10,2) DEFAULT 0.00,
  `total_weight` decimal(10,2) DEFAULT 0.00,
  `condition_status` enum('good', 'damaged', 'missing') DEFAULT 'good',
  `batch_number` varchar(50) NULL,
  `expiry_date` date NULL,
  `serial_numbers` text NULL,
  `keterangan` text NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_delivery_order_id` (`delivery_order_id`),
  KEY `idx_order_detail_id` (`order_detail_id`),
  KEY `idx_id_barang` (`id_barang`),
  KEY `idx_qty_delivered` (`qty_delivered`),
  KEY `idx_condition_status` (`condition_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- =====================================================
-- FOREIGN KEY CONSTRAINTS
-- =====================================================

-- Add foreign key constraints for delivery_orders
ALTER TABLE `delivery_orders` 
ADD CONSTRAINT `fk_delivery_orders_order_id` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
ADD CONSTRAINT `fk_delivery_orders_created_by` FOREIGN KEY (`created_by`) REFERENCES `tbl_user` (`id_user`) ON DELETE SET NULL ON UPDATE CASCADE,
ADD CONSTRAINT `fk_delivery_orders_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `tbl_user` (`id_user`) ON DELETE SET NULL ON UPDATE CASCADE,
ADD CONSTRAINT `fk_delivery_orders_approved_by` FOREIGN KEY (`approved_by`) REFERENCES `tbl_user` (`id_user`) ON DELETE SET NULL ON UPDATE CASCADE;

-- Add foreign key constraints for delivery_order_detail
ALTER TABLE `delivery_order_detail` 
ADD CONSTRAINT `fk_delivery_order_detail_delivery_order` FOREIGN KEY (`delivery_order_id`) REFERENCES `delivery_orders` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `fk_delivery_order_detail_order_detail` FOREIGN KEY (`order_detail_id`) REFERENCES `order_detail` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
ADD CONSTRAINT `fk_delivery_order_detail_barang` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- =====================================================
-- SAMPLE DATA (Optional - for testing)
-- =====================================================

-- Insert sample delivery order
INSERT INTO `delivery_orders` (
    `delivery_no`, `delivery_date`, `delivery_notes`, `order_id`, `order_no`, 
    `customer_name`, `customer_phone`, `customer_address`, `delivery_status`, 
    `priority`, `scheduled_date`, `driver_name`, `vehicle_info`, `tracking_number`, 
    `delivery_method`, `shipping_cost`, `total_items`, `total_qty`, 
    `delivery_instructions`, `recipient_name`, `signature_required`, 
    `delivery_fee`, `total_amount`, `created_by`
) VALUES (
    '***********', '2025-01-15', 'First delivery order for testing', 
    1, '001', 'Customer Test', '081234567890', 'Jl. Test No. 123, Jakarta', 
    'pending', 'normal', '2025-01-16', 'Driver Test', 'Truck B 1234 CD', 
    'TRK001', 'standard', 50000, 2, 8.00, 'Handle with care - fragile items', 
    'Customer Test', 'yes', 25000, 75000, 1
);

-- Insert sample delivery order details (assuming barang with id 1 and 2 exist)
INSERT INTO `delivery_order_detail` (
    `delivery_order_id`, `order_detail_id`, `id_barang`, `qty_ordered`, 
    `qty_delivered`, `qty_remaining`, `unit_weight`, `total_weight`, 
    `condition_status`, `keterangan`
) VALUES 
(1, 1, 1, 5.00, 5.00, 0.00, 1.50, 7.50, 'good', 'Complete delivery - all items in good condition'),
(1, 2, 2, 3.00, 3.00, 0.00, 2.00, 6.00, 'good', 'Complete delivery - all items in good condition');

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Show created tables
SHOW TABLES LIKE '%delivery%';

-- Show table structures
DESCRIBE delivery_orders;
DESCRIBE delivery_order_detail;

-- Show sample data
SELECT 'DELIVERY ORDERS' as table_name;
SELECT * FROM delivery_orders;

SELECT 'DELIVERY ORDER DETAILS' as table_name;
SELECT * FROM delivery_order_detail;

-- =====================================================
-- SUCCESS MESSAGE
-- =====================================================
SELECT 'Delivery Order tables created successfully!' as status;
SELECT 'Tables created: delivery_orders, delivery_order_detail' as info;
SELECT 'Foreign keys established with: orders, order_detail, barang, tbl_user' as relationships;
SELECT 'Sample data inserted for testing' as sample_data;
