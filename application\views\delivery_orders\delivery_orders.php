<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title"><i class="fa fa-truck text-blue"></i> Data Delivery Orders</h3>
                        <div class="text-right">
                            <button type="button" class="btn btn-sm btn-outline-primary add" onclick="add()" title="Add Data"><i class="fas fa-plus"></i> Add</button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <table id="tbl_delivery_orders" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr class="bg-info">
                                    <th>Delivery No</th>
                                    <th>Delivery Date</th>
                                    <th>Customer</th>
                                    <th>Status</th>
                                    <th>Priority</th>
                                    <th>Tracking</th>
                                    <th>Total</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->

<!-- Modal Form -->
<div class="modal fade" id="modal_form" role="dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Form Delivery Order</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body form">
                <form action="#" id="form" class="form-horizontal">
                    <input type="hidden" value="" name="id" />

                    <!-- Basic Information -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="fas fa-info-circle"></i> Basic Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label">Delivery Date <span class="text-red">*</span></label>
                                        <input name="delivery_date" type="date" class="form-control" required>
                                        <span class="help-block"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label">Priority</label>
                                        <select name="priority" class="form-control">
                                            <option value="low">Low</option>
                                            <option value="normal" selected>Normal</option>
                                            <option value="high">High</option>
                                            <option value="urgent">Urgent</option>
                                        </select>
                                        <span class="help-block"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label">Source Order</label>
                                        <select name="order_id" id="order_id" class="form-control">
                                            <option value="">Select Order (Optional)</option>
                                        </select>
                                        <span class="help-block"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label">Delivery Status</label>
                                        <select name="delivery_status" class="form-control">
                                            <option value="draft" selected>Draft</option>
                                            <option value="pending">Pending</option>
                                            <option value="in_transit">In Transit</option>
                                            <option value="delivered">Delivered</option>
                                            <option value="cancelled">Cancelled</option>
                                            <option value="returned">Returned</option>
                                        </select>
                                        <span class="help-block"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label">Delivery Notes</label>
                                <textarea name="delivery_notes" class="form-control" rows="3"></textarea>
                                <span class="help-block"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Customer Information -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="fas fa-user"></i> Customer Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label">Customer Name <span class="text-red">*</span></label>
                                        <input name="customer_name" type="text" class="form-control" required>
                                        <span class="help-block"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label">Customer Phone</label>
                                        <input name="customer_phone" type="text" class="form-control">
                                        <span class="help-block"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label">Customer Email</label>
                                        <input name="customer_email" type="email" class="form-control">
                                        <span class="help-block"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label">Recipient Name</label>
                                        <input name="recipient_name" type="text" class="form-control">
                                        <span class="help-block"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label">Customer Address</label>
                                <textarea name="customer_address" class="form-control" rows="3"></textarea>
                                <span class="help-block"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Delivery Information -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="fas fa-shipping-fast"></i> Delivery Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label">Scheduled Date</label>
                                        <input name="scheduled_date" type="date" class="form-control">
                                        <span class="help-block"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label">Delivery Method</label>
                                        <select name="delivery_method" class="form-control">
                                            <option value="standard" selected>Standard</option>
                                            <option value="express">Express</option>
                                            <option value="courier">Courier</option>
                                            <option value="pickup">Pickup</option>
                                        </select>
                                        <span class="help-block"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label">Driver Name</label>
                                        <input name="driver_name" type="text" class="form-control">
                                        <span class="help-block"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label">Driver Phone</label>
                                        <input name="driver_phone" type="text" class="form-control">
                                        <span class="help-block"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label">Vehicle Info</label>
                                        <input name="vehicle_info" type="text" class="form-control" placeholder="e.g., Truck B 1234 CD">
                                        <span class="help-block"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label">Tracking Number</label>
                                        <input name="tracking_number" type="text" class="form-control">
                                        <span class="help-block"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label">Delivery Instructions</label>
                                <textarea name="delivery_instructions" class="form-control" rows="3" placeholder="Special instructions for delivery..."></textarea>
                                <span class="help-block"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Financial Information -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="fas fa-dollar-sign"></i> Financial Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label">Shipping Cost</label>
                                        <input name="shipping_cost" type="number" step="0.01" class="form-control" value="0">
                                        <span class="help-block"></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label">Delivery Fee</label>
                                        <input name="delivery_fee" type="number" step="0.01" class="form-control" value="0">
                                        <span class="help-block"></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label">Insurance Fee</label>
                                        <input name="insurance_fee" type="number" step="0.01" class="form-control" value="0">
                                        <span class="help-block"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Information -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="fas fa-sticky-note"></i> Additional Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label class="control-label">Internal Notes</label>
                                <textarea name="internal_notes" class="form-control" rows="3" placeholder="Internal notes (not visible to customer)..."></textarea>
                                <span class="help-block"></span>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSave" onclick="save()" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<script type="text/javascript">
    var save_method; //for save method string
    var table;

    $(document).ready(function() {
        //datatables
        table = $("#tbl_delivery_orders").DataTable({
            "responsive": true,
            "autoWidth": false,
            "language": {
                "sEmptyTable": "Data Delivery Orders Belum Ada"
            },
            "processing": true, //Feature control the processing indicator.
            "serverSide": true, //Feature control DataTables' server-side processing mode.
            "order": [], //Initial no order.

            // Load data for the table's content from an Ajax source
            "ajax": {
                "url": "delivery_orders/ajax_list",
                "type": "POST"
            },
        });

        // Load available orders for dropdown
        loadAvailableOrders();

        // Order selection change event
        $('#order_id').change(function() {
            var order_id = $(this).val();
            if (order_id) {
                loadOrderDetails(order_id);
            } else {
                clearOrderDetails();
            }
        });

        //set input/textarea/select event when change value, remove class error and remove text help block
        $("input").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });
        $("textarea").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });
        $("select").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });
    });

    function loadAvailableOrders() {
        $.ajax({
            url: "delivery_orders/get_available_orders",
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                var options = '<option value="">Select Order (Optional)</option>';
                $.each(data, function(index, order) {
                    var deliveryStatus = '';
                    if (order.delivery_count > 0) {
                        if (order.delivered_count == order.delivery_count) {
                            deliveryStatus = ' [Fully Delivered]';
                        } else if (order.delivered_count > 0) {
                            deliveryStatus = ' [Partially Delivered: ' + order.delivered_count + '/' + order.delivery_count + ']';
                        } else {
                            deliveryStatus = ' [Pending Delivery: ' + order.delivery_count + ']';
                        }
                    } else {
                        deliveryStatus = ' [No Delivery Yet]';
                    }

                    options += '<option value="' + order.id + '">' +
                              order.order_no + ' - ' + order.customer_name +
                              ' (Rp ' + number_format(order.total_amount, 0, ',', '.') + ')' +
                              deliveryStatus + '</option>';
                });
                $('#order_id').html(options);
            }
        });
    }

    function loadOrderDetails(order_id) {
        $.ajax({
            url: "delivery_orders/get_order_details/" + order_id,
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                if (data.order) {
                    $('[name="order_no"]').val(data.order.order_no);
                    $('[name="customer_name"]').val(data.order.customer_name);
                    $('[name="customer_phone"]').val(data.order.customer_phone);
                    $('[name="customer_email"]').val(data.order.customer_email);
                    $('[name="customer_address"]').val(data.order.customer_address);
                }
            }
        });
    }

    function clearOrderDetails() {
        $('[name="order_no"]').val('');
        $('[name="customer_name"]').val('');
        $('[name="customer_phone"]').val('');
        $('[name="customer_email"]').val('');
        $('[name="customer_address"]').val('');
    }

    function add() {
        save_method = 'add';
        $('#form')[0].reset(); // reset form on modals
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $('#modal_form').modal('show'); // show bootstrap modal
        $('.modal-title').text('Add Delivery Order'); // set a title to modal

        // Set default values
        $('[name="delivery_date"]').val(new Date().toISOString().split('T')[0]);
        loadAvailableOrders();
    }

    function edit(id) {
        save_method = 'update';
        $('#form')[0].reset(); // reset form on modals
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string

        //Ajax Load data from ajax
        $.ajax({
            url: "delivery_orders/edit/" + id,
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                $('[name="id"]').val(data.id);
                $('[name="delivery_date"]').val(data.delivery_date);
                $('[name="delivery_notes"]').val(data.delivery_notes);
                $('[name="order_id"]').val(data.order_id);
                $('[name="order_no"]').val(data.order_no);
                $('[name="customer_name"]').val(data.customer_name);
                $('[name="customer_phone"]').val(data.customer_phone);
                $('[name="customer_email"]').val(data.customer_email);
                $('[name="customer_address"]').val(data.customer_address);
                $('[name="delivery_status"]').val(data.delivery_status);
                $('[name="priority"]').val(data.priority);
                $('[name="scheduled_date"]').val(data.scheduled_date);
                $('[name="driver_name"]').val(data.driver_name);
                $('[name="driver_phone"]').val(data.driver_phone);
                $('[name="vehicle_info"]').val(data.vehicle_info);
                $('[name="tracking_number"]').val(data.tracking_number);
                $('[name="delivery_method"]').val(data.delivery_method);
                $('[name="shipping_cost"]').val(data.shipping_cost);
                $('[name="delivery_instructions"]').val(data.delivery_instructions);
                $('[name="recipient_name"]').val(data.recipient_name);
                $('[name="delivery_fee"]').val(data.delivery_fee);
                $('[name="insurance_fee"]').val(data.insurance_fee);
                $('[name="internal_notes"]').val(data.internal_notes);

                $('#modal_form').modal('show'); // show bootstrap modal when complete loaded
                $('.modal-title').text('Edit Delivery Order'); // set a title to modal
                loadAvailableOrders();
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Error get data from ajax');
            }
        });
    }

    function detail(id) {
        window.location.href = "delivery_orders/detail/" + id;
    }

    function reload_table() {
        table.ajax.reload(null, false); //reload datatable ajax
    }

    function save() {
        $('#btnSave').text('saving...'); //change button text
        $('#btnSave').attr('disabled', true); //set button disable
        var url;

        if (save_method == 'add') {
            url = "delivery_orders/insert";
        } else {
            url = "delivery_orders/update";
        }

        // ajax adding data to database
        $.ajax({
            url: url,
            type: "POST",
            data: $('#form').serialize(),
            dataType: "JSON",
            success: function(data) {
                if (data.status) //if success close modal and reload ajax table
                {
                    $('#modal_form').modal('hide');
                    reload_table();
                    if (save_method == 'add') {
                        toastr.success('Delivery Order berhasil ditambahkan dengan nomor: ' + data.delivery_no);
                    } else {
                        toastr.success('Delivery Order berhasil diupdate');
                    }
                } else {
                    for (var i = 0; i < data.inputerror.length; i++) {
                        $('[name="' + data.inputerror[i] + '"]').parent().parent().addClass('has-error'); //select parent twice to select div form-group class and add has-error class
                        $('[name="' + data.inputerror[i] + '"]').next().text(data.error_string[i]); //select span help-block class set text error string
                        $('[name="' + data.inputerror[i] + '"]').addClass('is-invalid');
                    }
                }
                $('#btnSave').text('save'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Error adding / update data');
                $('#btnSave').text('save'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable
            }
        });
    }

    function hapus(id) {
        if (confirm('Are you sure delete this data?')) {
            // ajax delete data to database
            $.ajax({
                url: "delivery_orders/delete",
                type: "POST",
                data: {
                    id: id
                },
                dataType: "JSON",
                success: function(data) {
                    //if success reload ajax table
                    $('#modal_form').modal('hide');
                    reload_table();
                    toastr.success('Delivery Order berhasil dihapus');
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    alert('Error deleting data');
                }
            });
        }
    }

    function number_format(number, decimals, dec_point, thousands_sep) {
        number = (number + '').replace(',', '').replace(' ', '');
        var n = !isFinite(+number) ? 0 : +number,
            prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),
            sep = (typeof thousands_sep === 'undefined') ? ',' : thousands_sep,
            dec = (typeof dec_point === 'undefined') ? '.' : dec_point,
            s = '',
            toFixedFix = function(n, prec) {
                var k = Math.pow(10, prec);
                return '' + Math.round(n * k) / k;
            };
        s = (prec ? toFixedFix(n, prec) : '' + Math.round(n)).split('.');
        if (s[0].length > 3) {
            s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep);
        }
        if ((s[1] || '').length < prec) {
            s[1] = s[1] || '';
            s[1] += new Array(prec - s[1].length + 1).join('0');
        }
        return s.join(dec);
    }
</script>
