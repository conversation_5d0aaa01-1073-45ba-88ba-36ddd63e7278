-- Create table delivery_orders
CREATE TABLE IF NOT EXISTS `delivery_orders` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `delivery_no` varchar(255) NOT NULL,
  `delivery_date` date NOT NULL,
  `delivery_notes` text,
  `order_id` bigint(20) NULL,
  `order_no` varchar(255) NULL,
  `customer_name` varchar(255) NULL,
  `customer_phone` varchar(20) NULL,
  `customer_email` varchar(100) NULL,
  `customer_address` text NULL,
  `delivery_status` enum('draft', 'pending', 'in_transit', 'delivered', 'cancelled', 'returned') DEFAULT 'draft',
  `priority` enum('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
  `scheduled_date` date NULL,
  `actual_delivery_date` datetime NULL,
  `driver_name` varchar(255) NULL,
  `driver_phone` varchar(20) NULL,
  `vehicle_info` varchar(255) NULL,
  `tracking_number` varchar(100) NULL,
  `delivery_method` enum('pickup', 'courier', 'express', 'standard') DEFAULT 'standard',
  `shipping_cost` decimal(15,2) DEFAULT 0.00,
  `total_items` int(11) DEFAULT 0,
  `total_qty` decimal(10,2) DEFAULT 0.00,
  `total_weight` decimal(10,2) DEFAULT 0.00,
  `delivery_instructions` text NULL,
  `recipient_name` varchar(255) NULL,
  `recipient_phone` varchar(20) NULL,
  `signature_required` enum('yes', 'no') DEFAULT 'no',
  `proof_of_delivery` varchar(255) NULL,
  `delivery_fee` decimal(15,2) DEFAULT 0.00,
  `insurance_fee` decimal(15,2) DEFAULT 0.00,
  `total_amount` decimal(15,2) DEFAULT 0.00,
  `created_by` int(11) NULL,
  `updated_by` int(11) NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `approved_by` int(11) NULL,
  `approved_at` timestamp NULL,
  `cancelled_reason` text NULL,
  `internal_notes` text NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `delivery_no` (`delivery_no`),
  KEY `order_id` (`order_id`),
  KEY `delivery_status` (`delivery_status`),
  KEY `delivery_date` (`delivery_date`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  KEY `approved_by` (`approved_by`),
  CONSTRAINT `fk_delivery_orders_order_id` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_delivery_orders_created_by` FOREIGN KEY (`created_by`) REFERENCES `tbl_user` (`id_user`) ON DELETE SET NULL,
  CONSTRAINT `fk_delivery_orders_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `tbl_user` (`id_user`) ON DELETE SET NULL,
  CONSTRAINT `fk_delivery_orders_approved_by` FOREIGN KEY (`approved_by`) REFERENCES `tbl_user` (`id_user`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create table delivery_order_detail
CREATE TABLE IF NOT EXISTS `delivery_order_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `delivery_order_id` bigint(20) NOT NULL,
  `order_detail_id` bigint(20) NULL,
  `id_barang` int(11) NOT NULL,
  `qty_ordered` decimal(10,2) DEFAULT 0.00,
  `qty_delivered` decimal(10,2) NOT NULL DEFAULT 0.00,
  `qty_remaining` decimal(10,2) DEFAULT 0.00,
  `unit_weight` decimal(10,2) DEFAULT 0.00,
  `total_weight` decimal(10,2) DEFAULT 0.00,
  `condition_status` enum('good', 'damaged', 'missing') DEFAULT 'good',
  `batch_number` varchar(50) NULL,
  `expiry_date` date NULL,
  `serial_numbers` text NULL,
  `keterangan` text NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `delivery_order_id` (`delivery_order_id`),
  KEY `order_detail_id` (`order_detail_id`),
  KEY `id_barang` (`id_barang`),
  CONSTRAINT `delivery_order_detail_ibfk_1` FOREIGN KEY (`delivery_order_id`) REFERENCES `delivery_orders` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `delivery_order_detail_ibfk_2` FOREIGN KEY (`order_detail_id`) REFERENCES `order_detail` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `delivery_order_detail_ibfk_3` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Insert sample data (optional)
INSERT INTO `delivery_orders` (`delivery_no`, `delivery_date`, `delivery_notes`, `order_id`, `order_no`, `customer_name`, `customer_phone`, `customer_address`, `delivery_status`, `priority`, `scheduled_date`, `driver_name`, `vehicle_info`, `tracking_number`, `delivery_method`, `shipping_cost`, `total_items`, `total_qty`, `delivery_instructions`, `recipient_name`, `signature_required`, `delivery_fee`, `total_amount`, `created_by`) VALUES
('DO-001', '2025-01-15', 'First delivery order', 1, '001', 'Customer Test', '081234567890', 'Jl. Test No. 123', 'pending', 'normal', '2025-01-16', 'Driver Test', 'Truck B 1234 CD', 'TRK001', 'standard', 50000, 2, 8, 'Handle with care', 'Customer Test', 'yes', 25000, 75000, 1);

-- Insert sample detail data
INSERT INTO `delivery_order_detail` (`delivery_order_id`, `order_detail_id`, `id_barang`, `qty_ordered`, `qty_delivered`, `qty_remaining`, `unit_weight`, `total_weight`, `condition_status`, `keterangan`) VALUES
(1, 1, 1, 5, 5, 0, 1.5, 7.5, 'good', 'Complete delivery'),
(1, 2, 2, 3, 3, 0, 2.0, 6.0, 'good', 'Complete delivery');

-- Add indexes for better performance
CREATE INDEX idx_delivery_orders_customer ON delivery_orders(customer_name);
CREATE INDEX idx_delivery_orders_tracking ON delivery_orders(tracking_number);
CREATE INDEX idx_delivery_order_detail_qty ON delivery_order_detail(qty_delivered);
CREATE INDEX idx_delivery_order_detail_condition ON delivery_order_detail(condition_status);
