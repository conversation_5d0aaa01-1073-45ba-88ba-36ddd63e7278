# 🚀 **Panduan Instalasi Modul Delivery Order**

## 📋 **Langkah-langkah Instalasi**

### 1. **Buka phpMyAdmin**
- Akses phpMyAdmin di browser: `http://localhost/phpmyadmin`
- Login dengan username dan password MySQL Anda

### 2. **Pilih Database**
- Klik database `toko_elektronik` di panel kiri
- Pastikan Anda berada di database yang benar

### 3. **Import SQL Script**

#### **Opsi A: Import File SQL**
1. Klik tab **"Import"** di phpMyAdmin
2. Klik **"Choose File"** dan pilih file `create_delivery_orders_tables.sql`
3. Klik **"Go"** untuk menjalankan script

#### **Opsi B: Copy-Paste SQL**
1. Klik tab **"SQL"** di phpMyAdmin
2. Copy script SQL di bawah ini dan paste ke text area
3. Klik **"Go"** untuk menjalankan

### 4. **Script SQL untuk Copy-Paste**

```sql
-- =====================================================
-- DELIVERY ORDER TABLES - COPY PASTE VERSION
-- =====================================================

USE toko_elektronik;

-- Create delivery_orders table
CREATE TABLE IF NOT EXISTS `delivery_orders` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `delivery_no` varchar(255) NOT NULL,
  `delivery_date` date NOT NULL,
  `delivery_notes` text,
  `order_id` bigint(20) NULL,
  `order_no` varchar(255) NULL,
  `customer_name` varchar(255) NULL,
  `customer_phone` varchar(20) NULL,
  `customer_email` varchar(100) NULL,
  `customer_address` text NULL,
  `delivery_status` enum('draft', 'pending', 'in_transit', 'delivered', 'cancelled', 'returned') DEFAULT 'draft',
  `priority` enum('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
  `scheduled_date` date NULL,
  `actual_delivery_date` datetime NULL,
  `driver_name` varchar(255) NULL,
  `driver_phone` varchar(20) NULL,
  `vehicle_info` varchar(255) NULL,
  `tracking_number` varchar(100) NULL,
  `delivery_method` enum('pickup', 'courier', 'express', 'standard') DEFAULT 'standard',
  `shipping_cost` decimal(15,2) DEFAULT 0.00,
  `total_items` int(11) DEFAULT 0,
  `total_qty` decimal(10,2) DEFAULT 0.00,
  `total_weight` decimal(10,2) DEFAULT 0.00,
  `delivery_instructions` text NULL,
  `recipient_name` varchar(255) NULL,
  `recipient_phone` varchar(20) NULL,
  `signature_required` enum('yes', 'no') DEFAULT 'no',
  `proof_of_delivery` varchar(255) NULL,
  `delivery_fee` decimal(15,2) DEFAULT 0.00,
  `insurance_fee` decimal(15,2) DEFAULT 0.00,
  `total_amount` decimal(15,2) DEFAULT 0.00,
  `created_by` int(11) NULL,
  `updated_by` int(11) NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `approved_by` int(11) NULL,
  `approved_at` timestamp NULL,
  `cancelled_reason` text NULL,
  `internal_notes` text NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `delivery_no` (`delivery_no`),
  KEY `order_id` (`order_id`),
  KEY `delivery_status` (`delivery_status`),
  KEY `delivery_date` (`delivery_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create delivery_order_detail table
CREATE TABLE IF NOT EXISTS `delivery_order_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `delivery_order_id` bigint(20) NOT NULL,
  `order_detail_id` bigint(20) NULL,
  `id_barang` int(11) NOT NULL,
  `qty_ordered` decimal(10,2) DEFAULT 0.00,
  `qty_delivered` decimal(10,2) NOT NULL DEFAULT 0.00,
  `qty_remaining` decimal(10,2) DEFAULT 0.00,
  `unit_weight` decimal(10,2) DEFAULT 0.00,
  `total_weight` decimal(10,2) DEFAULT 0.00,
  `condition_status` enum('good', 'damaged', 'missing') DEFAULT 'good',
  `batch_number` varchar(50) NULL,
  `expiry_date` date NULL,
  `serial_numbers` text NULL,
  `keterangan` text NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `delivery_order_id` (`delivery_order_id`),
  KEY `order_detail_id` (`order_detail_id`),
  KEY `id_barang` (`id_barang`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Insert sample data
INSERT INTO `delivery_orders` (
    `delivery_no`, `delivery_date`, `delivery_notes`, `customer_name`, 
    `customer_phone`, `customer_address`, `delivery_status`, `priority`, 
    `scheduled_date`, `driver_name`, `vehicle_info`, `tracking_number`, 
    `delivery_method`, `shipping_cost`, `total_items`, `total_qty`, 
    `delivery_instructions`, `recipient_name`, `signature_required`, 
    `delivery_fee`, `total_amount`, `created_by`
) VALUES (
    '***********', '2025-01-15', 'First delivery order for testing', 
    'Customer Test', '081234567890', 'Jl. Test No. 123, Jakarta', 
    'pending', 'normal', '2025-01-16', 'Driver Test', 'Truck B 1234 CD', 
    'TRK001', 'standard', 50000, 2, 8, 'Handle with care', 
    'Customer Test', 'yes', 25000, 75000, 1
);

-- Show success message
SELECT 'Delivery Order tables created successfully!' as status;
```

### 5. **Verifikasi Instalasi**

Setelah menjalankan script, verifikasi dengan query berikut:

```sql
-- Cek tabel yang dibuat
SHOW TABLES LIKE '%delivery%';

-- Cek struktur tabel
DESCRIBE delivery_orders;
DESCRIBE delivery_order_detail;

-- Cek data sample
SELECT * FROM delivery_orders;
SELECT * FROM delivery_order_detail;
```

### 6. **Akses Modul**

Setelah tabel berhasil dibuat:
1. Buka browser dan akses: `http://localhost/toko_elektronik/delivery_orders`
2. Anda akan melihat halaman Delivery Orders dengan data sample
3. Coba fitur Add, Edit, Detail untuk memastikan semua berfungsi

## 🔧 **Troubleshooting**

### **Jika Ada Error Foreign Key:**
Gunakan script sederhana tanpa foreign key:

```sql
-- Script alternatif tanpa foreign key
USE toko_elektronik;

CREATE TABLE `delivery_orders` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `delivery_no` varchar(255) NOT NULL,
  `delivery_date` date NOT NULL,
  `customer_name` varchar(255) NULL,
  `delivery_status` enum('draft', 'pending', 'in_transit', 'delivered') DEFAULT 'draft',
  `total_amount` decimal(15,2) DEFAULT 0.00,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `delivery_order_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `delivery_order_id` bigint(20) NOT NULL,
  `id_barang` int(11) NOT NULL,
  `qty_delivered` decimal(10,2) NOT NULL DEFAULT 0.00,
  `condition_status` enum('good', 'damaged', 'missing') DEFAULT 'good',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
```

### **Jika Modul Tidak Muncul:**
1. Pastikan semua file PHP sudah di-upload ke folder yang benar
2. Cek permission folder dan file
3. Pastikan tidak ada syntax error di file PHP

## ✅ **Checklist Instalasi**

- [ ] Database toko_elektronik sudah ada
- [ ] Script SQL berhasil dijalankan
- [ ] Tabel delivery_orders dan delivery_order_detail terbuat
- [ ] Data sample berhasil diinsert
- [ ] File PHP controller dan model sudah di-upload
- [ ] File view sudah di-upload
- [ ] Modul bisa diakses di browser
- [ ] Fitur CRUD berfungsi normal

## 🎉 **Selesai!**

Modul Delivery Order siap digunakan dengan fitur lengkap:
- ✅ Manajemen delivery order
- ✅ Integrasi dengan sales order
- ✅ Tracking pengiriman
- ✅ Manajemen driver dan kendaraan
- ✅ UI modern dan responsive
