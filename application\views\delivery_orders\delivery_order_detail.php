<!-- Modern Minimalist CSS - Same as Sales Order -->
<style>
:root {
    --primary-color: #6366f1;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-900: #111827;
}

.modern-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border: 1px solid var(--gray-200);
    transition: all 0.2s ease;
}

.modern-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.stat-card {
    padding: 1.5rem;
    text-align: center;
    border-radius: 12px;
    background: white;
    border: 1px solid var(--gray-200);
    transition: all 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 12px;
    font-size: 20px;
    color: white;
}

.stat-icon.primary { background: linear-gradient(135deg, var(--primary-color), #8b5cf6); }
.stat-icon.success { background: linear-gradient(135deg, var(--success-color), #059669); }
.stat-icon.warning { background: linear-gradient(135deg, var(--warning-color), #d97706); }
.stat-icon.danger { background: linear-gradient(135deg, var(--danger-color), #dc2626); }

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
}

.modern-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge-draft { background: var(--gray-100); color: var(--gray-600); }
.badge-pending { background: #fef3c7; color: #92400e; }
.badge-in_transit { background: #e0e7ff; color: var(--primary-color); }
.badge-delivered { background: #d1fae5; color: #065f46; }
.badge-cancelled { background: #fee2e2; color: #991b1b; }
.badge-returned { background: var(--gray-100); color: var(--gray-600); }

.badge-low { background: #d1fae5; color: #065f46; }
.badge-normal { background: #dbeafe; color: #1e40af; }
.badge-high { background: #fef3c7; color: #92400e; }
.badge-urgent { background: #fee2e2; color: #991b1b; }

.badge-good { background: #d1fae5; color: #065f46; }
.badge-damaged { background: #fef3c7; color: #92400e; }
.badge-missing { background: #fee2e2; color: #991b1b; }

.compact-table {
    font-size: 0.875rem;
}

.compact-table th {
    background: var(--gray-50);
    border: none;
    padding: 0.75rem 1rem;
    font-weight: 600;
    color: var(--gray-700);
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
}

.compact-table td {
    padding: 1rem;
    border: none;
    border-bottom: 1px solid var(--gray-200);
    vertical-align: middle;
}

.compact-table tbody tr:hover {
    background: var(--gray-50);
}

.btn-modern {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    border: none;
}

.btn-modern:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.section-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--gray-200), transparent);
    margin: 2rem 0;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--gray-100);
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 500;
    color: var(--gray-600);
    font-size: 0.875rem;
}

.detail-value {
    font-weight: 600;
    color: var(--gray-900);
}

/* Horizontal Delivery Info Bar */
.delivery-info-bar {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border: 1px solid var(--gray-200);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.delivery-info-bar .row {
    align-items: stretch;
}

.delivery-info-bar .col-lg-3,
.delivery-info-bar .col-md-6,
.delivery-info-bar .col-sm-6 {
    display: flex;
}

.info-item {
    display: flex;
    align-items: flex-start;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    background: var(--gray-50);
    transition: all 0.2s ease;
    min-height: 80px;
    width: 100%;
    height: 100%;
}

.info-item:hover {
    background: var(--gray-100);
    transform: translateY(-1px);
}

.info-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    margin-top: 2px;
    font-size: 16px;
    color: white;
    flex-shrink: 0;
    position: relative;
    text-align: center;
    line-height: 1;
}

.info-icon i {
    display: block;
    width: 100%;
    text-align: center;
    vertical-align: middle;
}

.info-icon.primary { background: linear-gradient(135deg, var(--primary-color), #8b5cf6); }
.info-icon.success { background: linear-gradient(135deg, var(--success-color), #059669); }
.info-icon.warning { background: linear-gradient(135deg, var(--warning-color), #d97706); }
.info-icon.danger { background: linear-gradient(135deg, var(--danger-color), #dc2626); }

.info-content {
    flex: 1;
    min-width: 0;
    padding-top: 2px;
}

.info-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 2px;
}

.info-value {
    font-size: 1rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 4px;
    word-break: break-word;
}

.info-subtitle {
    font-size: 0.75rem;
    color: var(--gray-500);
    margin-bottom: 4px;
}

.info-badge {
    display: inline-block;
}

/* Additional Information Section */
.additional-info-bar {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border: 1px solid var(--gray-200);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.additional-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    cursor: pointer;
    transition: all 0.2s ease;
}

.additional-info-header:hover {
    background: var(--gray-100);
}

.additional-info-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-700);
    margin: 0;
    display: flex;
    align-items: center;
}

.additional-info-title i {
    margin-right: 8px;
    color: var(--primary-color);
}

.toggle-icon {
    transition: transform 0.2s ease;
    color: var(--gray-500);
}

.toggle-icon.rotated {
    transform: rotate(180deg);
}

.additional-info-content {
    padding: 1.5rem;
}

.additional-info-item {
    display: flex;
    align-items: flex-start;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    background: var(--gray-50);
    margin-bottom: 0.75rem;
    transition: all 0.2s ease;
}

.additional-info-item:last-child {
    margin-bottom: 0;
}

.additional-info-item:hover {
    background: var(--gray-100);
}

.additional-info-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 14px;
    color: white;
    flex-shrink: 0;
}

.additional-info-icon.info { background: linear-gradient(135deg, #3b82f6, #1d4ed8); }
.additional-info-icon.calendar { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }
.additional-info-icon.note { background: linear-gradient(135deg, #f59e0b, #d97706); }

.additional-info-details {
    flex: 1;
    min-width: 0;
}

.additional-info-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 4px;
}

.additional-info-value {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-900);
    line-height: 1.4;
    word-break: break-word;
}

/* Delivery Items Section */
.delivery-items-bar {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border: 1px solid var(--gray-200);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.delivery-items-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
}

.delivery-items-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-700);
    margin: 0;
    display: flex;
    align-items: center;
}

.delivery-items-title i {
    margin-right: 8px;
    color: var(--primary-color);
}

.btn-add-item {
    background: linear-gradient(135deg, var(--primary-color), #8b5cf6);
    border: none;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: white;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
}

.btn-add-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
    color: white;
}

.btn-add-item i {
    margin-right: 6px;
}

/* Enhanced Table Styling */
.delivery-items-table {
    margin: 0;
}

.delivery-items-table thead th {
    background: var(--gray-50);
    border: none;
    padding: 1rem;
    font-weight: 600;
    color: var(--gray-700);
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    border-bottom: 2px solid var(--gray-200);
}

.delivery-items-table tbody td {
    padding: 0.6rem 1rem;
    border: none;
    border-bottom: 1px solid var(--gray-100);
    vertical-align: middle;
    font-size: 0.875rem;
}

.delivery-items-table tbody tr {
    transition: all 0.2s ease;
}

.delivery-items-table tbody tr:hover {
    background: var(--gray-50);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.delivery-items-table tbody tr:last-child td {
    border-bottom: none;
}

/* Action Buttons */
.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    border: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin: 0 2px;
    transition: all 0.2s ease;
    font-size: 0.75rem;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.action-btn.edit {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.action-btn.delete {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

/* Summary Footer Enhancement */
.delivery-summary-footer {
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--gray-50), #f8fafc);
    border-top: 1px solid var(--gray-200);
}

.summary-stat-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem;
    border-radius: 8px;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.summary-stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.summary-stat-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    font-size: 14px;
    color: white;
    flex-shrink: 0;
}

.summary-stat-content {
    text-align: left;
}

.summary-stat-value {
    font-size: 1rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 2px;
}

.summary-stat-label {
    font-size: 0.75rem;
    color: var(--gray-600);
    font-weight: 500;
}

.final-total-card {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-left: 4px solid var(--primary-color);
}

.final-total-label {
    font-size: 0.75rem;
    color: var(--gray-600);
    font-weight: 500;
    margin-bottom: 4px;
}

.final-total-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 2px;
}

.final-total-note {
    font-size: 0.75rem;
    color: var(--gray-500);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .delivery-info-bar {
        padding: 1rem;
    }

    .info-item {
        padding: 0.5rem 0.75rem;
        margin-bottom: 0.5rem;
    }

    .info-icon {
        width: 32px;
        height: 32px;
        font-size: 14px;
        margin-right: 8px;
    }

    .info-value {
        font-size: 0.875rem;
    }

    .additional-info-content {
        padding: 1rem;
    }

    .additional-info-item {
        padding: 0.5rem 0.75rem;
    }

    .additional-info-icon {
        width: 28px;
        height: 28px;
        font-size: 12px;
        margin-right: 8px;
    }
}
</style>

<!-- Main content -->
<section class="content modern-container">
    <div class="container-fluid">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="mb-1 font-weight-bold text-gray-900">Delivery Order <?= $delivery_order->delivery_no ?></h2>
                <p class="text-muted mb-0"><?= date('d M Y, H:i', strtotime($delivery_order->delivery_date)) ?></p>
            </div>
            <a href="<?= base_url('delivery_orders') ?>" class="btn btn-outline-secondary btn-modern">
                <i class="fas fa-arrow-left mr-2"></i>Back
            </a>
        </div>

        <!-- Horizontal Delivery Info Bar -->
        <div class="delivery-info-bar">
            <div class="row g-3">
                <!-- Delivery Number -->
                <div class="col-lg-3 col-md-6 col-sm-6">
                    <div class="info-item">
                        <div class="info-icon primary">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-title">Delivery Number</div>
                            <div class="info-value"><?= $delivery_order->delivery_no ?></div>
                            <div class="info-subtitle"><?= date('d M Y', strtotime($delivery_order->delivery_date)) ?></div>
                            <div class="info-badge">
                                <span class="modern-badge badge-<?php
                                    switch($delivery_order->delivery_status) {
                                        case 'draft': echo 'draft'; break;
                                        case 'pending': echo 'pending'; break;
                                        case 'in_transit': echo 'in_transit'; break;
                                        case 'delivered': echo 'delivered'; break;
                                        case 'cancelled': echo 'cancelled'; break;
                                        case 'returned': echo 'returned'; break;
                                        default: echo 'draft';
                                    }
                                ?>"><?= ucfirst(str_replace('_', ' ', $delivery_order->delivery_status)) ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customer -->
                <div class="col-lg-3 col-md-6 col-sm-6">
                    <div class="info-item">
                        <div class="info-icon success">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-title">Customer</div>
                            <div class="info-value"><?= $delivery_order->customer_name ?: 'Walk-in Customer' ?></div>
                            <div class="info-subtitle"><?= $delivery_order->customer_phone ?: 'No phone' ?></div>
                        </div>
                    </div>
                </div>

                <!-- Priority -->
                <div class="col-lg-3 col-md-6 col-sm-6">
                    <div class="info-item">
                        <div class="info-icon warning">
                            <i class="fas fa-flag"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-title">Priority</div>
                            <div class="info-value">
                                <span class="modern-badge badge-<?php
                                    switch($delivery_order->priority) {
                                        case 'low': echo 'low'; break;
                                        case 'normal': echo 'normal'; break;
                                        case 'high': echo 'high'; break;
                                        case 'urgent': echo 'urgent'; break;
                                        default: echo 'normal';
                                    }
                                ?>"><?= ucfirst($delivery_order->priority) ?></span>
                            </div>
                            <div class="info-subtitle">Tracking: <?= $delivery_order->tracking_number ?: 'No tracking' ?></div>
                        </div>
                    </div>
                </div>

                <!-- Total Amount -->
                <div class="col-lg-3 col-md-6 col-sm-6">
                    <div class="info-item">
                        <div class="info-icon danger">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-title">Total Cost</div>
                            <div class="info-value">Rp <?= number_format($delivery_order->total_amount, 0, ',', '.') ?></div>
                            <div class="info-subtitle">Source: <?= $delivery_order->order_no ?: 'Manual' ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Information -->
        <?php if ($delivery_order->customer_email || $delivery_order->customer_address || $delivery_order->scheduled_date || $delivery_order->delivery_instructions || $delivery_order->internal_notes): ?>
        <div class="additional-info-bar">
            <div class="additional-info-header" data-toggle="collapse" data-target="#additionalInfo" aria-expanded="false">
                <h6 class="additional-info-title">
                    <i class="fas fa-info-circle"></i>
                    Additional Information
                </h6>
                <i class="fas fa-chevron-down toggle-icon"></i>
            </div>
            <div class="collapse" id="additionalInfo">
                <div class="additional-info-content">
                    <?php if ($delivery_order->customer_email || $delivery_order->customer_address): ?>
                    <div class="additional-info-item">
                        <div class="additional-info-icon info">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <div class="additional-info-details">
                            <div class="additional-info-label">Customer Details</div>
                            <div class="additional-info-value">
                                <?php if ($delivery_order->customer_email): ?>
                                    <strong>Email:</strong> <?= $delivery_order->customer_email ?><br>
                                <?php endif; ?>
                                <?php if ($delivery_order->customer_address): ?>
                                    <strong>Address:</strong> <?= $delivery_order->customer_address ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if ($delivery_order->scheduled_date || $delivery_order->actual_delivery_date): ?>
                    <div class="additional-info-item">
                        <div class="additional-info-icon calendar">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="additional-info-details">
                            <div class="additional-info-label">Important Dates</div>
                            <div class="additional-info-value">
                                <?php if ($delivery_order->scheduled_date): ?>
                                    <strong>Scheduled Date:</strong> <?= date('d M Y', strtotime($delivery_order->scheduled_date)) ?><br>
                                <?php endif; ?>
                                <?php if ($delivery_order->actual_delivery_date): ?>
                                    <strong>Actual Delivery:</strong> <?= date('d M Y H:i', strtotime($delivery_order->actual_delivery_date)) ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if ($delivery_order->delivery_instructions || $delivery_order->internal_notes): ?>
                    <div class="additional-info-item">
                        <div class="additional-info-icon note">
                            <i class="fas fa-sticky-note"></i>
                        </div>
                        <div class="additional-info-details">
                            <div class="additional-info-label">Notes & Instructions</div>
                            <div class="additional-info-value">
                                <?php if ($delivery_order->delivery_instructions): ?>
                                    <strong>Delivery Instructions:</strong> <?= nl2br($delivery_order->delivery_instructions) ?><br>
                                <?php endif; ?>
                                <?php if ($delivery_order->internal_notes): ?>
                                    <strong>Internal Notes:</strong> <?= nl2br($delivery_order->internal_notes) ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Delivery Items -->
        <div class="delivery-items-bar" style="padding: 15px; margin-bottom: 20px;">
            <div class="delivery-items-header">
                <h6 class="delivery-items-title">
                    <i class="fas fa-boxes"></i>
                    Delivery Items
                </h6>
                <button type="button" class="btn-add-item" onclick="add_detail()">
                    <i class="fas fa-plus"></i>Add Item
                </button>
            </div>

            <div class="table-responsive">
                <table id="tbl_delivery_order_detail" class="delivery-items-table table mb-0">
                    <thead>
                        <tr>
                            <th>Item</th>
                            <th>Unit</th>
                            <th class="text-center">Qty Ordered</th>
                            <th class="text-center">Qty Delivered</th>
                            <th class="text-center">Remaining</th>
                            <th>Condition</th>
                            <th>Notes</th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>

            <!-- Enhanced Summary Footer -->
            <div class="delivery-summary-footer">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-4">
                                <div class="summary-stat-item">
                                    <div class="summary-stat-icon primary" style="background: linear-gradient(135deg, var(--primary-color), #8b5cf6);">
                                        <i class="fas fa-boxes"></i>
                                    </div>
                                    <div class="summary-stat-content">
                                        <div class="summary-stat-value" id="total-items"><?= $delivery_order->total_items ?></div>
                                        <div class="summary-stat-label">Items</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="summary-stat-item">
                                    <div class="summary-stat-icon success" style="background: linear-gradient(135deg, var(--success-color), #059669);">
                                        <i class="fas fa-sort-numeric-up"></i>
                                    </div>
                                    <div class="summary-stat-content">
                                        <div class="summary-stat-value" id="total-qty"><?= number_format($delivery_order->total_qty, 2) ?></div>
                                        <div class="summary-stat-label">Quantity</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="summary-stat-item">
                                    <div class="summary-stat-icon warning" style="background: linear-gradient(135deg, var(--warning-color), #d97706);">
                                        <i class="fas fa-weight"></i>
                                    </div>
                                    <div class="summary-stat-content">
                                        <div class="summary-stat-value" id="total-weight"><?= number_format($delivery_order->total_weight, 2) ?> kg</div>
                                        <div class="summary-stat-label">Weight</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="final-total-card">
                            <div class="final-total-label" id="final-total-label">Total Cost</div>
                            <div class="final-total-value">
                                Rp <?= number_format($delivery_order->total_amount, 0, ',', '.') ?>
                            </div>
                            <div class="final-total-note">Inc. shipping & fees</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modal Form Detail -->
<div class="modal fade" id="modal_form_detail" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Form Delivery Item</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body form">
                <form action="#" id="form_detail" class="form-horizontal">
                    <input type="hidden" value="" name="id" />
                    <input type="hidden" value="<?= $delivery_order->id ?>" name="delivery_order_id" />

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="control-label">Item <span class="text-red">*</span></label>
                                <select name="id_barang" class="form-control" required>
                                    <option value="">Select Item</option>
                                </select>
                                <span class="help-block"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="control-label">Order Detail Reference</label>
                                <select name="order_detail_id" class="form-control">
                                    <option value="">Select Order Detail (Optional)</option>
                                </select>
                                <span class="help-block"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="control-label">Qty Ordered</label>
                                <input name="qty_ordered" type="number" step="0.01" class="form-control" value="0">
                                <span class="help-block"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="control-label">Qty Delivered <span class="text-red">*</span></label>
                                <input name="qty_delivered" type="number" step="0.01" class="form-control" required>
                                <span class="help-block"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="control-label">Unit Weight (kg)</label>
                                <input name="unit_weight" type="number" step="0.01" class="form-control" value="0">
                                <span class="help-block"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="control-label">Condition Status</label>
                                <select name="condition_status" class="form-control">
                                    <option value="good" selected>Good</option>
                                    <option value="damaged">Damaged</option>
                                    <option value="missing">Missing</option>
                                </select>
                                <span class="help-block"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="control-label">Batch Number</label>
                                <input name="batch_number" type="text" class="form-control">
                                <span class="help-block"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="control-label">Expiry Date</label>
                                <input name="expiry_date" type="date" class="form-control">
                                <span class="help-block"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="control-label">Serial Numbers</label>
                                <input name="serial_numbers" type="text" class="form-control" placeholder="Comma separated">
                                <span class="help-block"></span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label">Notes</label>
                        <textarea name="keterangan" class="form-control" rows="3"></textarea>
                        <span class="help-block"></span>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSaveDetail" onclick="save_detail()" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    var save_method_detail;
    var table_detail;
    var delivery_order_id = <?= $delivery_order->id ?>;

    $(document).ready(function() {
        //datatables for delivery order detail
        table_detail = $("#tbl_delivery_order_detail").DataTable({
            "responsive": true,
            "autoWidth": false,
            "language": {
                "sEmptyTable": "Belum Ada Item Delivery"
            },
            "processing": true,
            "serverSide": true,
            "order": [],

            "ajax": {
                "url": "<?= base_url('delivery_orders/ajax_list_detail') ?>",
                "type": "POST",
                "data": function(d) {
                    d.delivery_order_id = delivery_order_id;
                }
            },
            "drawCallback": function(settings) {
                updateDeliveryOrderSummary();
            }
        });

        // Additional Information toggle animation
        $('#additionalInfo').on('show.bs.collapse', function () {
            $('.toggle-icon').addClass('rotated');
        });

        $('#additionalInfo').on('hide.bs.collapse', function () {
            $('.toggle-icon').removeClass('rotated');
        });

        // Load items for dropdown
        loadItems();

        // Load order details if source order exists
        <?php if ($delivery_order->order_id): ?>
        loadOrderDetails(<?= $delivery_order->order_id ?>);
        <?php endif; ?>

        //set input/textarea/select event when change value, remove class error and remove text help block
        $("input").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });
        $("textarea").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });
        $("select").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });
    });

    function loadItems() {
        $.ajax({
            url: "<?= base_url('barang/get_all') ?>",
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                var options = '<option value="">Select Item</option>';
                $.each(data, function(index, item) {
                    options += '<option value="' + item.id + '">' + item.nama + '</option>';
                });
                $('[name="id_barang"]').html(options);
            }
        });
    }

    function loadOrderDetails(order_id) {
        $.ajax({
            url: "<?= base_url('delivery_orders/get_order_details/') ?>" + order_id,
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                if (data.details) {
                    var options = '<option value="">Select Order Detail (Optional)</option>';
                    $.each(data.details, function(index, detail) {
                        options += '<option value="' + detail.id + '" data-qty="' + detail.qty + '" data-barang="' + detail.id_barang + '">' + detail.nama_barang + ' (Qty: ' + detail.qty + ')</option>';
                    });
                    $('[name="order_detail_id"]').html(options);

                    // Auto-fill when order detail is selected
                    $('[name="order_detail_id"]').change(function() {
                        var selected = $(this).find(':selected');
                        if (selected.val()) {
                            $('[name="qty_ordered"]').val(selected.data('qty'));
                            $('[name="id_barang"]').val(selected.data('barang'));
                        }
                    });
                }
            }
        });
    }

    function updateDeliveryOrderSummary() {
        $.ajax({
            url: "<?= base_url('delivery_orders/get_delivery_order_summary') ?>",
            type: "POST",
            data: {delivery_order_id: delivery_order_id},
            dataType: "JSON",
            success: function(data) {
                $('#total-items').text(data.total_items);
                $('#total-qty').text(parseFloat(data.total_qty).toFixed(2));
                $('#total-weight').text(parseFloat(data.total_weight).toFixed(2) + ' kg');
            }
        });
    }

    function add_detail() {
        save_method_detail = 'add';
        $('#form_detail')[0].reset();
        $('.form-group').removeClass('has-error');
        $('.help-block').empty();
        $('#modal_form_detail').modal('show');
        $('.modal-title').text('Add Delivery Item');
        loadItems();
    }

    function edit_detail(id) {
        save_method_detail = 'update';
        $('#form_detail')[0].reset();
        $('.form-group').removeClass('has-error');
        $('.help-block').empty();

        $.ajax({
            url: "<?= base_url('delivery_orders/edit_detail/') ?>" + id,
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                $('[name="id"]').val(data.id);
                $('[name="order_detail_id"]').val(data.order_detail_id);
                $('[name="id_barang"]').val(data.id_barang);
                $('[name="qty_ordered"]').val(data.qty_ordered);
                $('[name="qty_delivered"]').val(data.qty_delivered);
                $('[name="unit_weight"]').val(data.unit_weight);
                $('[name="condition_status"]').val(data.condition_status);
                $('[name="batch_number"]').val(data.batch_number);
                $('[name="expiry_date"]').val(data.expiry_date);
                $('[name="serial_numbers"]').val(data.serial_numbers);
                $('[name="keterangan"]').val(data.keterangan);

                $('#modal_form_detail').modal('show');
                $('.modal-title').text('Edit Delivery Item');
                loadItems();
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Error get data from ajax');
            }
        });
    }

    function reload_table_detail() {
        table_detail.ajax.reload(null, false);
    }

    function save_detail() {
        $('#btnSaveDetail').text('saving...');
        $('#btnSaveDetail').attr('disabled', true);
        var url;

        if (save_method_detail == 'add') {
            url = "<?= base_url('delivery_orders/insert_detail') ?>";
        } else {
            url = "<?= base_url('delivery_orders/update_detail') ?>";
        }

        $.ajax({
            url: url,
            type: "POST",
            data: $('#form_detail').serialize(),
            dataType: "JSON",
            success: function(data) {
                if (data.status) {
                    $('#modal_form_detail').modal('hide');
                    reload_table_detail();
                    if (save_method_detail == 'add') {
                        toastr.success('Delivery item berhasil ditambahkan');
                    } else {
                        toastr.success('Delivery item berhasil diupdate');
                    }
                } else {
                    for (var i = 0; i < data.inputerror.length; i++) {
                        $('[name="' + data.inputerror[i] + '"]').parent().parent().addClass('has-error');
                        $('[name="' + data.inputerror[i] + '"]').next().text(data.error_string[i]);
                        $('[name="' + data.inputerror[i] + '"]').addClass('is-invalid');
                    }
                }
                $('#btnSaveDetail').text('save');
                $('#btnSaveDetail').attr('disabled', false);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Error adding / update data');
                $('#btnSaveDetail').text('save');
                $('#btnSaveDetail').attr('disabled', false);
            }
        });
    }

    function hapus_detail(id) {
        if (confirm('Are you sure delete this item?')) {
            $.ajax({
                url: "<?= base_url('delivery_orders/delete_detail') ?>",
                type: "POST",
                data: {
                    id: id,
                    delivery_order_id: delivery_order_id
                },
                dataType: "JSON",
                success: function(data) {
                    $('#modal_form_detail').modal('hide');
                    reload_table_detail();
                    toastr.success('Delivery item berhasil dihapus');
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    alert('Error deleting data');
                }
            });
        }
    }

    function number_format(number, decimals, dec_point, thousands_sep) {
        number = (number + '').replace(',', '').replace(' ', '');
        var n = !isFinite(+number) ? 0 : +number,
            prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),
            sep = (typeof thousands_sep === 'undefined') ? ',' : thousands_sep,
            dec = (typeof dec_point === 'undefined') ? '.' : dec_point,
            s = '',
            toFixedFix = function(n, prec) {
                var k = Math.pow(10, prec);
                return '' + Math.round(n * k) / k;
            };
        s = (prec ? toFixedFix(n, prec) : '' + Math.round(n)).split('.');
        if (s[0].length > 3) {
            s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep);
        }
        if ((s[1] || '').length < prec) {
            s[1] = s[1] || '';
            s[1] += new Array(prec - s[1].length + 1).join('0');
        }
        return s.join(dec);
    }
</script>
