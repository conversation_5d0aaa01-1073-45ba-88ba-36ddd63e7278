<style>
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
}

.modern-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.card-modern {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    background: white;
    margin-bottom: 2rem;
}

.card-header-modern {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    padding: 1.5rem;
    border: none;
}

.card-body-modern {
    padding: 2rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.info-item {
    background: var(--gray-100);
    padding: 1rem;
    border-radius: 10px;
    border-left: 4px solid var(--primary-color);
}

.info-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-600);
    margin-bottom: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 1rem;
    font-weight: 500;
    color: var(--gray-800);
    word-break: break-word;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-draft { background: var(--gray-200); color: var(--gray-700); }
.status-pending { background: #fff3cd; color: #856404; }
.status-in_transit { background: #d1ecf1; color: #0c5460; }
.status-delivered { background: #d4edda; color: #155724; }
.status-cancelled { background: #f8d7da; color: #721c24; }
.status-returned { background: var(--gray-300); color: var(--gray-800); }

.priority-low { background: var(--gray-200); color: var(--gray-700); }
.priority-normal { background: #cce5ff; color: #004085; }
.priority-high { background: #fff3cd; color: #856404; }
.priority-urgent { background: #f8d7da; color: #721c24; }

.btn-modern {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: none;
    transition: all 0.3s ease;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.table-modern {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.table-modern thead th {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: none;
    padding: 1rem;
}

.table-modern tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-color: var(--gray-200);
}

.table-modern tbody tr:hover {
    background: var(--gray-100);
}

.summary-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    margin-bottom: 2rem;
}

.summary-item {
    margin-bottom: 1rem;
}

.summary-label {
    font-size: 0.875rem;
    opacity: 0.9;
    margin-bottom: 0.25rem;
}

.summary-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.toggle-icon {
    transition: transform 0.3s ease;
}

.toggle-icon.rotated {
    transform: rotate(180deg);
}

.additional-info {
    background: var(--gray-100);
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 1rem;
}

.condition-good { background: #d4edda; color: #155724; }
.condition-damaged { background: #fff3cd; color: #856404; }
.condition-missing { background: #f8d7da; color: #721c24; }
</style>

<!-- Main content -->
<section class="content modern-container">
    <div class="container-fluid">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="mb-1 font-weight-bold text-white">Delivery Order <?= $delivery_order->delivery_no ?></h2>
                <p class="text-white-50 mb-0"><?= date('d M Y, H:i', strtotime($delivery_order->delivery_date)) ?></p>
            </div>
            <a href="<?= base_url('delivery_orders') ?>" class="btn btn-outline-light btn-modern">
                <i class="fas fa-arrow-left mr-2"></i>Back
            </a>
        </div>

        <!-- Delivery Order Information -->
        <div class="card card-modern">
            <div class="card-header card-header-modern">
                <h4 class="mb-0"><i class="fas fa-truck mr-2"></i>Delivery Order Information</h4>
            </div>
            <div class="card-body card-body-modern">
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Delivery Number</div>
                        <div class="info-value"><?= $delivery_order->delivery_no ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Delivery Date</div>
                        <div class="info-value"><?= date('d M Y', strtotime($delivery_order->delivery_date)) ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Status</div>
                        <div class="info-value">
                            <span class="status-badge status-<?= $delivery_order->delivery_status ?>"><?= ucfirst(str_replace('_', ' ', $delivery_order->delivery_status)) ?></span>
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Priority</div>
                        <div class="info-value">
                            <span class="status-badge priority-<?= $delivery_order->priority ?>"><?= ucfirst($delivery_order->priority) ?></span>
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Source Order</div>
                        <div class="info-value"><?= $delivery_order->order_no ? $delivery_order->order_no : '-' ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Tracking Number</div>
                        <div class="info-value"><?= $delivery_order->tracking_number ? $delivery_order->tracking_number : '-' ?></div>
                    </div>
                </div>

                <?php if ($delivery_order->delivery_notes): ?>
                <div class="info-item">
                    <div class="info-label">Delivery Notes</div>
                    <div class="info-value"><?= nl2br(htmlspecialchars($delivery_order->delivery_notes)) ?></div>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Customer & Delivery Information -->
        <div class="row">
            <div class="col-md-6">
                <div class="card card-modern">
                    <div class="card-header card-header-modern">
                        <h5 class="mb-0"><i class="fas fa-user mr-2"></i>Customer Information</h5>
                    </div>
                    <div class="card-body card-body-modern">
                        <div class="info-item mb-3">
                            <div class="info-label">Customer Name</div>
                            <div class="info-value"><?= $delivery_order->customer_name ?></div>
                        </div>
                        <div class="info-item mb-3">
                            <div class="info-label">Phone</div>
                            <div class="info-value"><?= $delivery_order->customer_phone ? $delivery_order->customer_phone : '-' ?></div>
                        </div>
                        <div class="info-item mb-3">
                            <div class="info-label">Email</div>
                            <div class="info-value"><?= $delivery_order->customer_email ? $delivery_order->customer_email : '-' ?></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Address</div>
                            <div class="info-value"><?= $delivery_order->customer_address ? nl2br(htmlspecialchars($delivery_order->customer_address)) : '-' ?></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card card-modern">
                    <div class="card-header card-header-modern">
                        <h5 class="mb-0"><i class="fas fa-shipping-fast mr-2"></i>Delivery Information</h5>
                    </div>
                    <div class="card-body card-body-modern">
                        <div class="info-item mb-3">
                            <div class="info-label">Delivery Method</div>
                            <div class="info-value"><?= ucfirst($delivery_order->delivery_method) ?></div>
                        </div>
                        <div class="info-item mb-3">
                            <div class="info-label">Scheduled Date</div>
                            <div class="info-value"><?= $delivery_order->scheduled_date ? date('d M Y', strtotime($delivery_order->scheduled_date)) : '-' ?></div>
                        </div>
                        <div class="info-item mb-3">
                            <div class="info-label">Driver</div>
                            <div class="info-value"><?= $delivery_order->driver_name ? $delivery_order->driver_name : '-' ?></div>
                        </div>
                        <div class="info-item mb-3">
                            <div class="info-label">Vehicle</div>
                            <div class="info-value"><?= $delivery_order->vehicle_info ? $delivery_order->vehicle_info : '-' ?></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Recipient</div>
                            <div class="info-value"><?= $delivery_order->recipient_name ? $delivery_order->recipient_name : $delivery_order->customer_name ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Delivery Summary -->
        <div class="summary-card">
            <div class="row">
                <div class="col-md-3">
                    <div class="summary-item">
                        <div class="summary-label">Total Items</div>
                        <div class="summary-value" id="total-items"><?= $delivery_order->total_items ?></div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="summary-item">
                        <div class="summary-label">Total Quantity</div>
                        <div class="summary-value" id="total-qty"><?= number_format($delivery_order->total_qty, 2) ?></div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="summary-item">
                        <div class="summary-label">Total Weight</div>
                        <div class="summary-value" id="total-weight"><?= number_format($delivery_order->total_weight, 2) ?> kg</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="summary-item">
                        <div class="summary-label">Total Cost</div>
                        <div class="summary-value" id="total-cost">Rp <?= number_format($delivery_order->total_amount, 0, ',', '.') ?></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Delivery Items -->
        <div class="card card-modern">
            <div class="card-header card-header-modern d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-boxes mr-2"></i>Delivery Items</h5>
                <button type="button" class="btn btn-light btn-sm btn-modern" onclick="add_detail()" title="Add Item">
                    <i class="fas fa-plus mr-1"></i>Add Item
                </button>
            </div>
            <div class="card-body card-body-modern">
                <div class="table-responsive">
                    <table id="tbl_delivery_order_detail" class="table table-modern">
                        <thead>
                            <tr>
                                <th>Item</th>
                                <th>Unit</th>
                                <th>Qty Ordered</th>
                                <th>Qty Delivered</th>
                                <th>Remaining</th>
                                <th>Condition</th>
                                <th>Notes</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Additional Information -->
        <div class="card card-modern">
            <div class="card-header card-header-modern">
                <h5 class="mb-0">
                    <button class="btn btn-link text-white p-0" type="button" data-toggle="collapse" data-target="#additionalInfo" aria-expanded="false">
                        <i class="fas fa-chevron-down toggle-icon mr-2"></i>Additional Information
                    </button>
                </h5>
            </div>
            <div class="collapse" id="additionalInfo">
                <div class="card-body card-body-modern">
                    <div class="additional-info">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-item mb-3">
                                    <div class="info-label">Shipping Cost</div>
                                    <div class="info-value">Rp <?= number_format($delivery_order->shipping_cost, 0, ',', '.') ?></div>
                                </div>
                                <div class="info-item mb-3">
                                    <div class="info-label">Delivery Fee</div>
                                    <div class="info-value">Rp <?= number_format($delivery_order->delivery_fee, 0, ',', '.') ?></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Insurance Fee</div>
                                    <div class="info-value">Rp <?= number_format($delivery_order->insurance_fee, 0, ',', '.') ?></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <?php if ($delivery_order->delivery_instructions): ?>
                                <div class="info-item mb-3">
                                    <div class="info-label">Delivery Instructions</div>
                                    <div class="info-value"><?= nl2br(htmlspecialchars($delivery_order->delivery_instructions)) ?></div>
                                </div>
                                <?php endif; ?>
                                <?php if ($delivery_order->internal_notes): ?>
                                <div class="info-item">
                                    <div class="info-label">Internal Notes</div>
                                    <div class="info-value"><?= nl2br(htmlspecialchars($delivery_order->internal_notes)) ?></div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modal Form Detail -->
<div class="modal fade" id="modal_form_detail" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Form Delivery Item</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body form">
                <form action="#" id="form_detail" class="form-horizontal">
                    <input type="hidden" value="" name="id" />
                    <input type="hidden" value="<?= $delivery_order->id ?>" name="delivery_order_id" />

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="control-label">Item <span class="text-red">*</span></label>
                                <select name="id_barang" class="form-control" required>
                                    <option value="">Select Item</option>
                                </select>
                                <span class="help-block"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="control-label">Order Detail Reference</label>
                                <select name="order_detail_id" class="form-control">
                                    <option value="">Select Order Detail (Optional)</option>
                                </select>
                                <span class="help-block"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="control-label">Qty Ordered</label>
                                <input name="qty_ordered" type="number" step="0.01" class="form-control" value="0">
                                <span class="help-block"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="control-label">Qty Delivered <span class="text-red">*</span></label>
                                <input name="qty_delivered" type="number" step="0.01" class="form-control" required>
                                <span class="help-block"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="control-label">Unit Weight (kg)</label>
                                <input name="unit_weight" type="number" step="0.01" class="form-control" value="0">
                                <span class="help-block"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="control-label">Condition Status</label>
                                <select name="condition_status" class="form-control">
                                    <option value="good" selected>Good</option>
                                    <option value="damaged">Damaged</option>
                                    <option value="missing">Missing</option>
                                </select>
                                <span class="help-block"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="control-label">Batch Number</label>
                                <input name="batch_number" type="text" class="form-control">
                                <span class="help-block"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="control-label">Expiry Date</label>
                                <input name="expiry_date" type="date" class="form-control">
                                <span class="help-block"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="control-label">Serial Numbers</label>
                                <input name="serial_numbers" type="text" class="form-control" placeholder="Comma separated">
                                <span class="help-block"></span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label">Notes</label>
                        <textarea name="keterangan" class="form-control" rows="3"></textarea>
                        <span class="help-block"></span>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSaveDetail" onclick="save_detail()" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    var save_method_detail;
    var table_detail;
    var delivery_order_id = <?= $delivery_order->id ?>;

    $(document).ready(function() {
        //datatables for delivery order detail
        table_detail = $("#tbl_delivery_order_detail").DataTable({
            "responsive": true,
            "autoWidth": false,
            "language": {
                "sEmptyTable": "Belum Ada Item Delivery"
            },
            "processing": true,
            "serverSide": true,
            "order": [],

            "ajax": {
                "url": "<?= base_url('delivery_orders/ajax_list_detail') ?>",
                "type": "POST",
                "data": function(d) {
                    d.delivery_order_id = delivery_order_id;
                }
            },
            "drawCallback": function(settings) {
                updateDeliveryOrderSummary();
            }
        });

        // Additional Information toggle animation
        $('#additionalInfo').on('show.bs.collapse', function () {
            $('.toggle-icon').addClass('rotated');
        });

        $('#additionalInfo').on('hide.bs.collapse', function () {
            $('.toggle-icon').removeClass('rotated');
        });

        // Load items for dropdown
        loadItems();

        // Load order details if source order exists
        <?php if ($delivery_order->order_id): ?>
        loadOrderDetails(<?= $delivery_order->order_id ?>);
        <?php endif; ?>

        //set input/textarea/select event when change value, remove class error and remove text help block
        $("input").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });
        $("textarea").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });
        $("select").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });
    });

    function loadItems() {
        $.ajax({
            url: "<?= base_url('barang/get_all') ?>",
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                var options = '<option value="">Select Item</option>';
                $.each(data, function(index, item) {
                    options += '<option value="' + item.id + '">' + item.nama + '</option>';
                });
                $('[name="id_barang"]').html(options);
            }
        });
    }

    function loadOrderDetails(order_id) {
        $.ajax({
            url: "<?= base_url('delivery_orders/get_order_details/') ?>" + order_id,
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                if (data.details) {
                    var options = '<option value="">Select Order Detail (Optional)</option>';
                    $.each(data.details, function(index, detail) {
                        options += '<option value="' + detail.id + '" data-qty="' + detail.qty + '" data-barang="' + detail.id_barang + '">' + detail.nama_barang + ' (Qty: ' + detail.qty + ')</option>';
                    });
                    $('[name="order_detail_id"]').html(options);

                    // Auto-fill when order detail is selected
                    $('[name="order_detail_id"]').change(function() {
                        var selected = $(this).find(':selected');
                        if (selected.val()) {
                            $('[name="qty_ordered"]').val(selected.data('qty'));
                            $('[name="id_barang"]').val(selected.data('barang'));
                        }
                    });
                }
            }
        });
    }

    function updateDeliveryOrderSummary() {
        $.ajax({
            url: "<?= base_url('delivery_orders/get_delivery_order_summary') ?>",
            type: "POST",
            data: {delivery_order_id: delivery_order_id},
            dataType: "JSON",
            success: function(data) {
                $('#total-items').text(data.total_items);
                $('#total-qty').text(parseFloat(data.total_qty).toFixed(2));
                $('#total-weight').text(parseFloat(data.total_weight).toFixed(2) + ' kg');
            }
        });
    }

    function add_detail() {
        save_method_detail = 'add';
        $('#form_detail')[0].reset();
        $('.form-group').removeClass('has-error');
        $('.help-block').empty();
        $('#modal_form_detail').modal('show');
        $('.modal-title').text('Add Delivery Item');
        loadItems();
    }

    function edit_detail(id) {
        save_method_detail = 'update';
        $('#form_detail')[0].reset();
        $('.form-group').removeClass('has-error');
        $('.help-block').empty();

        $.ajax({
            url: "<?= base_url('delivery_orders/edit_detail/') ?>" + id,
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                $('[name="id"]').val(data.id);
                $('[name="order_detail_id"]').val(data.order_detail_id);
                $('[name="id_barang"]').val(data.id_barang);
                $('[name="qty_ordered"]').val(data.qty_ordered);
                $('[name="qty_delivered"]').val(data.qty_delivered);
                $('[name="unit_weight"]').val(data.unit_weight);
                $('[name="condition_status"]').val(data.condition_status);
                $('[name="batch_number"]').val(data.batch_number);
                $('[name="expiry_date"]').val(data.expiry_date);
                $('[name="serial_numbers"]').val(data.serial_numbers);
                $('[name="keterangan"]').val(data.keterangan);

                $('#modal_form_detail').modal('show');
                $('.modal-title').text('Edit Delivery Item');
                loadItems();
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Error get data from ajax');
            }
        });
    }

    function reload_table_detail() {
        table_detail.ajax.reload(null, false);
    }

    function save_detail() {
        $('#btnSaveDetail').text('saving...');
        $('#btnSaveDetail').attr('disabled', true);
        var url;

        if (save_method_detail == 'add') {
            url = "<?= base_url('delivery_orders/insert_detail') ?>";
        } else {
            url = "<?= base_url('delivery_orders/update_detail') ?>";
        }

        $.ajax({
            url: url,
            type: "POST",
            data: $('#form_detail').serialize(),
            dataType: "JSON",
            success: function(data) {
                if (data.status) {
                    $('#modal_form_detail').modal('hide');
                    reload_table_detail();
                    if (save_method_detail == 'add') {
                        toastr.success('Delivery item berhasil ditambahkan');
                    } else {
                        toastr.success('Delivery item berhasil diupdate');
                    }
                } else {
                    for (var i = 0; i < data.inputerror.length; i++) {
                        $('[name="' + data.inputerror[i] + '"]').parent().parent().addClass('has-error');
                        $('[name="' + data.inputerror[i] + '"]').next().text(data.error_string[i]);
                        $('[name="' + data.inputerror[i] + '"]').addClass('is-invalid');
                    }
                }
                $('#btnSaveDetail').text('save');
                $('#btnSaveDetail').attr('disabled', false);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Error adding / update data');
                $('#btnSaveDetail').text('save');
                $('#btnSaveDetail').attr('disabled', false);
            }
        });
    }

    function hapus_detail(id) {
        if (confirm('Are you sure delete this item?')) {
            $.ajax({
                url: "<?= base_url('delivery_orders/delete_detail') ?>",
                type: "POST",
                data: {
                    id: id,
                    delivery_order_id: delivery_order_id
                },
                dataType: "JSON",
                success: function(data) {
                    $('#modal_form_detail').modal('hide');
                    reload_table_detail();
                    toastr.success('Delivery item berhasil dihapus');
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    alert('Error deleting data');
                }
            });
        }
    }

    function number_format(number, decimals, dec_point, thousands_sep) {
        number = (number + '').replace(',', '').replace(' ', '');
        var n = !isFinite(+number) ? 0 : +number,
            prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),
            sep = (typeof thousands_sep === 'undefined') ? ',' : thousands_sep,
            dec = (typeof dec_point === 'undefined') ? '.' : dec_point,
            s = '',
            toFixedFix = function(n, prec) {
                var k = Math.pow(10, prec);
                return '' + Math.round(n * k) / k;
            };
        s = (prec ? toFixedFix(n, prec) : '' + Math.round(n)).split('.');
        if (s[0].length > 3) {
            s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep);
        }
        if ((s[1] || '').length < prec) {
            s[1] = s[1] || '';
            s[1] += new Array(prec - s[1].length + 1).join('0');
        }
        return s.join(dec);
    }
</script>
