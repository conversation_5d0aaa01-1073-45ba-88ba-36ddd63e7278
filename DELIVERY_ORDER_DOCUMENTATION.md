# 📦 **Delivery Order Module Documentation**

## 🎯 **Overview**
Modul Delivery Order adalah sistem manajemen pengiriman yang terintegrasi dengan Sales Order. Modul ini memungkinkan tracking dan pengelolaan pengiriman barang dari warehouse ke customer dengan fitur lengkap dan modern UI.

## 🗂️ **Database Structure**

### 📋 **Tabel delivery_orders**
```sql
- id (Primary Key)
- delivery_no (VARCHAR) - Nomor delivery order otomatis
- delivery_date (DATE) - Tanggal pengiriman
- delivery_notes (TEXT) - Catatan pengiriman
- order_id (BIGINT, FK to orders) - Referensi ke sales order
- order_no (VARCHAR) - Nomor sales order
- customer_name (VARCHAR) - Nama customer
- customer_phone (VARCHAR) - Telepon customer
- customer_email (VARCHAR) - Email customer
- customer_address (TEXT) - Alamat customer
- delivery_status (ENUM) - Status pengiriman
- priority (ENUM) - Prioritas pengiriman
- scheduled_date (DATE) - <PERSON>gal jadwal pengiriman
- actual_delivery_date (DATETIME) - Tanggal aktual pengiriman
- driver_name (VARCHAR) - <PERSON>a driver
- driver_phone (VARCHAR) - Telepon driver
- vehicle_info (VARCHAR) - Info kendaraan
- tracking_number (VARCHAR) - Nomor tracking
- delivery_method (ENUM) - Metode pengiriman
- shipping_cost (DECIMAL) - Biaya pengiriman
- total_items (INT) - Total item
- total_qty (DECIMAL) - Total quantity
- total_weight (DECIMAL) - Total berat
- delivery_instructions (TEXT) - Instruksi pengiriman
- recipient_name (VARCHAR) - Nama penerima
- recipient_phone (VARCHAR) - Telepon penerima
- signature_required (ENUM) - Perlu tanda tangan
- proof_of_delivery (VARCHAR) - Bukti pengiriman
- delivery_fee (DECIMAL) - Biaya delivery
- insurance_fee (DECIMAL) - Biaya asuransi
- total_amount (DECIMAL) - Total biaya
- created_by (INT, FK to tbl_user)
- updated_by (INT, FK to tbl_user)
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
- approved_by (INT, FK to tbl_user)
- approved_at (TIMESTAMP)
- cancelled_reason (TEXT)
- internal_notes (TEXT)
```

### 📋 **Tabel delivery_order_detail**
```sql
- id (Primary Key)
- delivery_order_id (BIGINT, FK to delivery_orders)
- order_detail_id (BIGINT, FK to order_detail) - Referensi ke order detail
- id_barang (INT, FK to barang)
- qty_ordered (DECIMAL) - Quantity yang dipesan
- qty_delivered (DECIMAL) - Quantity yang dikirim
- qty_remaining (DECIMAL) - Quantity yang tersisa
- unit_weight (DECIMAL) - Berat per unit
- total_weight (DECIMAL) - Total berat
- condition_status (ENUM) - Status kondisi barang
- batch_number (VARCHAR) - Nomor batch
- expiry_date (DATE) - Tanggal kadaluarsa
- serial_numbers (TEXT) - Nomor serial
- keterangan (TEXT) - Keterangan
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

## 🔄 **Workflow Delivery Order**

### 1. **Create Delivery Order**
- Status: Draft
- Priority: Normal
- Delivery Method: Standard
- Bisa dari Sales Order atau manual

### 2. **Add Delivery Items**
- Tambah items yang akan dikirim
- Set quantity delivered
- Track kondisi barang (good/damaged/missing)
- Auto-calculate weight dan totals

### 3. **Set Delivery Details**
- Assign driver dan kendaraan
- Set tracking number
- Delivery instructions
- Financial details (shipping cost, fees)

### 4. **Process Delivery**
- Change status to Pending → In Transit
- Update actual delivery date
- Track delivery progress

### 5. **Complete Delivery**
- Status: Delivered
- Proof of delivery
- Customer signature (if required)

## 🎨 **UI Features**

### ✨ **Modern Design**
- Gradient backgrounds
- Card-based layout
- Responsive design
- Clean typography
- Status badges dengan warna

### 📱 **Responsive Layout**
- Mobile-friendly
- Tablet optimized
- Desktop enhanced
- Touch-friendly buttons

### 🎯 **User Experience**
- Intuitive navigation
- Real-time updates
- Auto-calculations
- Smart form validations
- Toast notifications

## 🔗 **Integration dengan Sales Order**

### 📊 **Relationship**
- One Sales Order → Many Delivery Orders
- Delivery Order Detail → Order Detail (optional reference)
- Auto-populate customer data dari Sales Order
- Track delivery progress per order

### 🔄 **Data Flow**
1. Select Sales Order (optional)
2. Auto-fill customer information
3. Load order items untuk reference
4. Create delivery items
5. Track delivery status

## 📁 **File Structure**

### 🗄️ **Database**
- `delivery_orders_table.sql` - Script create tables

### 🎮 **Controllers**
- `application/controllers/Delivery_orders.php` - Main controller

### 🏗️ **Models**
- `application/models/Mod_delivery_orders.php` - Data access layer

### 🎨 **Views**
- `application/views/delivery_orders/delivery_orders.php` - Main list view
- `application/views/delivery_orders/delivery_order_detail.php` - Detail view

## 🚀 **Key Features**

### 📦 **Delivery Management**
- Auto-generate delivery numbers (DO-YYYY0001)
- Multiple delivery methods
- Driver assignment
- Vehicle tracking
- Real-time status updates

### 📊 **Tracking & Monitoring**
- Delivery status tracking
- Priority management
- Weight calculations
- Cost tracking
- Proof of delivery

### 🎯 **Item Management**
- Quantity tracking (ordered vs delivered)
- Condition monitoring
- Batch/serial number tracking
- Expiry date management
- Detailed notes

### 💰 **Financial Tracking**
- Shipping costs
- Delivery fees
- Insurance fees
- Total cost calculations
- Cost breakdown

## 🔧 **Technical Features**

### 🛠️ **Backend**
- CodeIgniter 3 framework
- MySQL database
- RESTful API endpoints
- Server-side DataTables
- AJAX operations

### 🎨 **Frontend**
- Bootstrap 4
- jQuery
- DataTables
- Font Awesome icons
- Toastr notifications

### 🔒 **Security**
- User authentication
- Role-based access
- CSRF protection
- SQL injection prevention
- XSS protection

## 📈 **Performance**

### ⚡ **Optimizations**
- Database indexing
- Lazy loading
- Pagination
- Caching strategies
- Optimized queries

### 📊 **Monitoring**
- Real-time summaries
- Auto-calculations
- Progress tracking
- Status monitoring

## 🎯 **Usage Instructions**

### 1. **Installation**
```sql
-- Run database script
SOURCE delivery_orders_table.sql;
```

### 2. **Access Module**
- Navigate to `/delivery_orders`
- Add new delivery order
- Manage delivery items
- Track delivery progress

### 3. **Integration**
- Link to existing Sales Orders
- Auto-populate customer data
- Reference order items
- Track delivery completion

## 🔮 **Future Enhancements**

### 📱 **Mobile App**
- Driver mobile app
- Real-time GPS tracking
- Photo proof of delivery
- Digital signatures

### 🤖 **Automation**
- Auto-assign drivers
- Route optimization
- Delivery scheduling
- SMS/Email notifications

### 📊 **Analytics**
- Delivery performance metrics
- Driver performance tracking
- Customer satisfaction scores
- Cost analysis reports

## 🎉 **Conclusion**

Modul Delivery Order menyediakan solusi lengkap untuk manajemen pengiriman dengan:
- ✅ UI modern dan responsive
- ✅ Integrasi seamless dengan Sales Order
- ✅ Tracking lengkap dari order sampai delivery
- ✅ Manajemen driver dan kendaraan
- ✅ Financial tracking yang detail
- ✅ Real-time monitoring dan updates

Modul ini mengikuti pattern yang sama dengan Sales Order untuk konsistensi dan kemudahan maintenance.
