-- =====================================================
-- DELIVERY ORDER MODULE - SIMPLE VERSION
-- =====================================================
-- Run this if the main script has foreign key issues
-- =====================================================

USE toko_elektronik;

-- Create delivery_orders table
CREATE TABLE IF NOT EXISTS `delivery_orders` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `delivery_no` varchar(255) NOT NULL,
  `delivery_date` date NOT NULL,
  `delivery_notes` text,
  `order_id` bigint(20) NULL,
  `order_no` varchar(255) NULL,
  `customer_name` varchar(255) NULL,
  `customer_phone` varchar(20) NULL,
  `customer_email` varchar(100) NULL,
  `customer_address` text NULL,
  `delivery_status` enum('draft', 'pending', 'in_transit', 'delivered', 'cancelled', 'returned') DEFAULT 'draft',
  `priority` enum('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
  `scheduled_date` date NULL,
  `actual_delivery_date` datetime NULL,
  `driver_name` varchar(255) NULL,
  `driver_phone` varchar(20) NULL,
  `vehicle_info` varchar(255) NULL,
  `tracking_number` varchar(100) NULL,
  `delivery_method` enum('pickup', 'courier', 'express', 'standard') DEFAULT 'standard',
  `shipping_cost` decimal(15,2) DEFAULT 0.00,
  `total_items` int(11) DEFAULT 0,
  `total_qty` decimal(10,2) DEFAULT 0.00,
  `total_weight` decimal(10,2) DEFAULT 0.00,
  `delivery_instructions` text NULL,
  `recipient_name` varchar(255) NULL,
  `recipient_phone` varchar(20) NULL,
  `signature_required` enum('yes', 'no') DEFAULT 'no',
  `proof_of_delivery` varchar(255) NULL,
  `delivery_fee` decimal(15,2) DEFAULT 0.00,
  `insurance_fee` decimal(15,2) DEFAULT 0.00,
  `total_amount` decimal(15,2) DEFAULT 0.00,
  `created_by` int(11) NULL,
  `updated_by` int(11) NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `approved_by` int(11) NULL,
  `approved_at` timestamp NULL,
  `cancelled_reason` text NULL,
  `internal_notes` text NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `delivery_no` (`delivery_no`),
  KEY `order_id` (`order_id`),
  KEY `delivery_status` (`delivery_status`),
  KEY `delivery_date` (`delivery_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create delivery_order_detail table
CREATE TABLE IF NOT EXISTS `delivery_order_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `delivery_order_id` bigint(20) NOT NULL,
  `order_detail_id` bigint(20) NULL,
  `id_barang` int(11) NOT NULL,
  `qty_ordered` decimal(10,2) DEFAULT 0.00,
  `qty_delivered` decimal(10,2) NOT NULL DEFAULT 0.00,
  `qty_remaining` decimal(10,2) DEFAULT 0.00,
  `unit_weight` decimal(10,2) DEFAULT 0.00,
  `total_weight` decimal(10,2) DEFAULT 0.00,
  `condition_status` enum('good', 'damaged', 'missing') DEFAULT 'good',
  `batch_number` varchar(50) NULL,
  `expiry_date` date NULL,
  `serial_numbers` text NULL,
  `keterangan` text NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `delivery_order_id` (`delivery_order_id`),
  KEY `order_detail_id` (`order_detail_id`),
  KEY `id_barang` (`id_barang`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Insert sample data
INSERT INTO `delivery_orders` (`delivery_no`, `delivery_date`, `delivery_notes`, `customer_name`, `customer_phone`, `customer_address`, `delivery_status`, `priority`, `scheduled_date`, `driver_name`, `vehicle_info`, `tracking_number`, `delivery_method`, `shipping_cost`, `total_items`, `total_qty`, `delivery_instructions`, `recipient_name`, `signature_required`, `delivery_fee`, `total_amount`, `created_by`) VALUES
('***********', '2025-01-15', 'First delivery order', 'Customer Test', '081234567890', 'Jl. Test No. 123', 'pending', 'normal', '2025-01-16', 'Driver Test', 'Truck B 1234 CD', 'TRK001', 'standard', 50000, 2, 8, 'Handle with care', 'Customer Test', 'yes', 25000, 75000, 1);

-- Show success message
SELECT 'Delivery Order tables created successfully!' as status;
