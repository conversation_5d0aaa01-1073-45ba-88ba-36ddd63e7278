<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Delivery_orders extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('Mod_delivery_orders');
    }

    public function index()
    {
        $link = 'delivery_orders';
        $level = $this->session->userdata('id_level');

        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();
        if ($jml > 0) {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        }

        if ($akses == 'Y') {
            $this->template->load('layoutbackend', 'delivery_orders/delivery_orders', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        $list = $this->Mod_delivery_orders->get_datatables();
        $data = array();
        $no = $_POST['start'];
        foreach ($list as $delivery_order) {
            $no++;
            $row = array();
            $row[] = $delivery_order->delivery_no;
            $row[] = date('d M Y', strtotime($delivery_order->delivery_date));
            $row[] = $delivery_order->customer_name;
            $row[] = '<span class="badge badge-' . $this->get_status_color($delivery_order->delivery_status) . '">' . ucfirst($delivery_order->delivery_status) . '</span>';
            $row[] = '<span class="badge badge-' . $this->get_priority_color($delivery_order->priority) . '">' . ucfirst($delivery_order->priority) . '</span>';
            $row[] = $delivery_order->tracking_number ? $delivery_order->tracking_number : '-';
            $row[] = 'Rp ' . number_format($delivery_order->total_amount, 0, ',', '.');

            // Action buttons
            $row[] = '<div class="btn-group">
                        <button type="button" class="btn btn-sm btn-info" onclick="detail(' . $delivery_order->id . ')" title="Detail"><i class="fas fa-eye"></i></button>
                        <button type="button" class="btn btn-sm btn-warning" onclick="edit(' . $delivery_order->id . ')" title="Edit"><i class="fas fa-edit"></i></button>
                        <button type="button" class="btn btn-sm btn-danger" onclick="hapus(' . $delivery_order->id . ')" title="Delete"><i class="fas fa-trash"></i></button>
                      </div>';

            $data[] = $row;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_delivery_orders->count_all(),
            "recordsFiltered" => $this->Mod_delivery_orders->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    private function get_status_color($status)
    {
        switch ($status) {
            case 'draft': return 'secondary';
            case 'pending': return 'warning';
            case 'in_transit': return 'info';
            case 'delivered': return 'success';
            case 'cancelled': return 'danger';
            case 'returned': return 'dark';
            default: return 'secondary';
        }
    }

    private function get_priority_color($priority)
    {
        switch ($priority) {
            case 'low': return 'secondary';
            case 'normal': return 'primary';
            case 'high': return 'warning';
            case 'urgent': return 'danger';
            default: return 'primary';
        }
    }

    public function insert()
    {
        $delivery_no = $this->Mod_delivery_orders->generate_delivery_no();

        $save = array(
            'delivery_no' => $delivery_no,
            'delivery_date' => $this->input->post('delivery_date'),
            'delivery_notes' => $this->input->post('delivery_notes'),
            'order_id' => $this->input->post('order_id'),
            'order_no' => $this->input->post('order_no'),
            'customer_name' => $this->input->post('customer_name'),
            'customer_phone' => $this->input->post('customer_phone'),
            'customer_email' => $this->input->post('customer_email'),
            'customer_address' => $this->input->post('customer_address'),
            'delivery_status' => $this->input->post('delivery_status') ? $this->input->post('delivery_status') : 'draft',
            'priority' => $this->input->post('priority') ? $this->input->post('priority') : 'normal',
            'scheduled_date' => $this->input->post('scheduled_date'),
            'driver_name' => $this->input->post('driver_name'),
            'driver_phone' => $this->input->post('driver_phone'),
            'vehicle_info' => $this->input->post('vehicle_info'),
            'tracking_number' => $this->input->post('tracking_number'),
            'delivery_method' => $this->input->post('delivery_method') ? $this->input->post('delivery_method') : 'standard',
            'shipping_cost' => $this->input->post('shipping_cost') ? $this->input->post('shipping_cost') : 0,
            'delivery_instructions' => $this->input->post('delivery_instructions'),
            'recipient_name' => $this->input->post('recipient_name'),
            'recipient_phone' => $this->input->post('recipient_phone'),
            'signature_required' => $this->input->post('signature_required') ? $this->input->post('signature_required') : 'no',
            'delivery_fee' => $this->input->post('delivery_fee') ? $this->input->post('delivery_fee') : 0,
            'insurance_fee' => $this->input->post('insurance_fee') ? $this->input->post('insurance_fee') : 0,
            'internal_notes' => $this->input->post('internal_notes'),
            'created_by' => $this->session->userdata('id_user'),
        );

        $this->Mod_delivery_orders->insert('delivery_orders', $save);
        echo json_encode(array("status" => TRUE, "delivery_no" => $delivery_no));
    }

    public function update()
    {
        $id = $this->input->post('id');
        $save = array(
            'delivery_date' => $this->input->post('delivery_date'),
            'delivery_notes' => $this->input->post('delivery_notes'),
            'order_id' => $this->input->post('order_id'),
            'order_no' => $this->input->post('order_no'),
            'customer_name' => $this->input->post('customer_name'),
            'customer_phone' => $this->input->post('customer_phone'),
            'customer_email' => $this->input->post('customer_email'),
            'customer_address' => $this->input->post('customer_address'),
            'delivery_status' => $this->input->post('delivery_status'),
            'priority' => $this->input->post('priority'),
            'scheduled_date' => $this->input->post('scheduled_date'),
            'actual_delivery_date' => $this->input->post('actual_delivery_date'),
            'driver_name' => $this->input->post('driver_name'),
            'driver_phone' => $this->input->post('driver_phone'),
            'vehicle_info' => $this->input->post('vehicle_info'),
            'tracking_number' => $this->input->post('tracking_number'),
            'delivery_method' => $this->input->post('delivery_method'),
            'shipping_cost' => $this->input->post('shipping_cost'),
            'delivery_instructions' => $this->input->post('delivery_instructions'),
            'recipient_name' => $this->input->post('recipient_name'),
            'recipient_phone' => $this->input->post('recipient_phone'),
            'signature_required' => $this->input->post('signature_required'),
            'delivery_fee' => $this->input->post('delivery_fee'),
            'insurance_fee' => $this->input->post('insurance_fee'),
            'cancelled_reason' => $this->input->post('cancelled_reason'),
            'internal_notes' => $this->input->post('internal_notes'),
            'updated_by' => $this->session->userdata('id_user'),
        );
        $this->Mod_delivery_orders->update($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit($id)
    {
        $data = $this->Mod_delivery_orders->get($id);
        echo json_encode($data);
    }

    public function delete()
    {
        $id = $this->input->post('id');
        $this->Mod_delivery_orders->delete($id, 'delivery_orders');
        echo json_encode(array("status" => TRUE));
    }

    // Detail view
    public function detail($id)
    {
        $data['delivery_order'] = $this->Mod_delivery_orders->get($id);
        if (!$data['delivery_order']) {
            show_404();
        }
        $this->template->load('layoutbackend', 'delivery_orders/delivery_order_detail', $data);
    }

    // Get available orders for dropdown
    public function get_available_orders()
    {
        $orders = $this->Mod_delivery_orders->get_available_orders();
        echo json_encode($orders);
    }

    // Get order details when order is selected
    public function get_order_details($order_id)
    {
        $order = $this->db->get_where('orders', array('id' => $order_id))->row();
        $details = $this->Mod_delivery_orders->get_order_details($order_id);

        echo json_encode(array(
            'order' => $order,
            'details' => $details
        ));
    }

    // Detail management methods
    public function ajax_list_detail()
    {
        $list = $this->Mod_delivery_orders->get_datatables_detail();
        $data = array();
        $no = $_POST['start'];
        foreach ($list as $detail) {
            $no++;
            $row = array();
            $row[] = $detail->nama_barang;
            $row[] = $detail->nama_satuan;
            $row[] = number_format($detail->qty_ordered, 2);
            $row[] = number_format($detail->qty_delivered, 2);
            $row[] = number_format($detail->qty_remaining, 2);
            $row[] = '<span class="badge badge-' . $this->get_condition_color($detail->condition_status) . '">' . ucfirst($detail->condition_status) . '</span>';
            $row[] = $detail->keterangan;

            // Action buttons
            $row[] = '<div class="btn-group">
                        <button type="button" class="btn btn-sm btn-warning" onclick="edit_detail(' . $detail->id . ')" title="Edit"><i class="fas fa-edit"></i></button>
                        <button type="button" class="btn btn-sm btn-danger" onclick="hapus_detail(' . $detail->id . ')" title="Delete"><i class="fas fa-trash"></i></button>
                      </div>';

            $data[] = $row;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_delivery_orders->count_all_detail(),
            "recordsFiltered" => $this->Mod_delivery_orders->count_filtered_detail(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    private function get_condition_color($condition)
    {
        switch ($condition) {
            case 'good': return 'success';
            case 'damaged': return 'warning';
            case 'missing': return 'danger';
            default: return 'secondary';
        }
    }

    public function insert_detail()
    {
        $delivery_order_id = $this->input->post('delivery_order_id');
        $qty_delivered = $this->input->post('qty_delivered');
        $unit_weight = $this->input->post('unit_weight') ? $this->input->post('unit_weight') : 0;

        $save = array(
            'delivery_order_id' => $delivery_order_id,
            'order_detail_id' => $this->input->post('order_detail_id'),
            'id_barang' => $this->input->post('id_barang'),
            'qty_ordered' => $this->input->post('qty_ordered'),
            'qty_delivered' => $qty_delivered,
            'qty_remaining' => $this->input->post('qty_ordered') - $qty_delivered,
            'unit_weight' => $unit_weight,
            'total_weight' => $qty_delivered * $unit_weight,
            'condition_status' => $this->input->post('condition_status') ? $this->input->post('condition_status') : 'good',
            'batch_number' => $this->input->post('batch_number'),
            'expiry_date' => $this->input->post('expiry_date'),
            'serial_numbers' => $this->input->post('serial_numbers'),
            'keterangan' => $this->input->post('keterangan'),
        );

        $this->Mod_delivery_orders->insert_detail('delivery_order_detail', $save);

        // Update delivery order totals
        $this->Mod_delivery_orders->update_totals($delivery_order_id);

        echo json_encode(array("status" => TRUE));
    }

    public function update_detail()
    {
        $id = $this->input->post('id');
        $delivery_order_id = $this->input->post('delivery_order_id');
        $qty_delivered = $this->input->post('qty_delivered');
        $unit_weight = $this->input->post('unit_weight') ? $this->input->post('unit_weight') : 0;

        $save = array(
            'order_detail_id' => $this->input->post('order_detail_id'),
            'id_barang' => $this->input->post('id_barang'),
            'qty_ordered' => $this->input->post('qty_ordered'),
            'qty_delivered' => $qty_delivered,
            'qty_remaining' => $this->input->post('qty_ordered') - $qty_delivered,
            'unit_weight' => $unit_weight,
            'total_weight' => $qty_delivered * $unit_weight,
            'condition_status' => $this->input->post('condition_status'),
            'batch_number' => $this->input->post('batch_number'),
            'expiry_date' => $this->input->post('expiry_date'),
            'serial_numbers' => $this->input->post('serial_numbers'),
            'keterangan' => $this->input->post('keterangan'),
        );

        $this->Mod_delivery_orders->update_detail($id, $save);

        // Update delivery order totals
        $this->Mod_delivery_orders->update_totals($delivery_order_id);

        echo json_encode(array("status" => TRUE));
    }

    public function edit_detail($id)
    {
        $data = $this->Mod_delivery_orders->get_detail($id);
        echo json_encode($data);
    }

    public function delete_detail()
    {
        $id = $this->input->post('id');
        $delivery_order_id = $this->input->post('delivery_order_id');

        $this->Mod_delivery_orders->delete_detail($id, 'delivery_order_detail');

        // Update delivery order totals
        $this->Mod_delivery_orders->update_totals($delivery_order_id);

        echo json_encode(array("status" => TRUE));
    }

    public function get_delivery_order_summary()
    {
        $delivery_order_id = $this->input->post('delivery_order_id');
        $summary = $this->Mod_delivery_orders->get_delivery_order_summary($delivery_order_id);
        echo json_encode($summary);
    }

    public function calculate_total()
    {
        $delivery_order_id = $this->input->post('delivery_order_id');
        $delivery_order = $this->Mod_delivery_orders->get($delivery_order_id);

        $summary = $this->Mod_delivery_orders->get_delivery_order_summary($delivery_order_id);
        $total_amount = $delivery_order->shipping_cost + $delivery_order->delivery_fee + $delivery_order->insurance_fee;

        echo json_encode(array(
            'total_amount' => $total_amount,
            'summary' => $summary
        ));
    }
}
