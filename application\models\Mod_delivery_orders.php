<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Mod_delivery_orders extends CI_Model
{
    var $table = 'delivery_orders';
    var $column_search = array('delivery_no', 'delivery_date', 'customer_name', 'customer_phone', 'delivery_status', 'priority', 'tracking_number', 'driver_name');
    var $column_order = array('delivery_no', 'delivery_date', 'customer_name', 'delivery_status', 'priority', 'tracking_number', 'total_amount');
    var $order = array('id' => 'desc');

    function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    private function _get_datatables_query()
    {
        $this->db->from($this->table);
        $i = 0;
        foreach ($this->column_search as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }
                if (count($this->column_search) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order)) {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables()
    {
        $this->_get_datatables_query();
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }

    function count_all()
    {
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }

    function insert($table, $data)
    {
        return $this->db->insert($table, $data);
    }

    function update($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table, $data);
    }

    function get($id)
    {
        $this->db->where('id', $id);
        return $this->db->get($this->table)->row();
    }

    function delete($id, $table)
    {
        $this->db->where('id', $id);
        $this->db->delete($table);
    }

    // Methods for delivery order detail
    var $table_detail = 'delivery_order_detail';
    var $column_search_detail = array('b.nama', 'c.nama', 'a.qty_delivered', 'a.condition_status', 'a.keterangan');
    var $column_order_detail = array('b.nama', 'c.nama', 'a.qty_ordered', 'a.qty_delivered', 'a.condition_status', 'a.keterangan');
    var $order_detail = array('a.id' => 'desc');

    private function _get_datatables_query_detail()
    {
        $this->db->select('a.*, b.nama as nama_barang, c.nama as nama_satuan');
        $this->db->from($this->table_detail . ' a');
        $this->db->join('barang b', 'a.id_barang = b.id', 'left');
        $this->db->join('satuan c', 'b.id_satuan = c.id', 'left');
        $this->db->where('a.delivery_order_id', $_POST['delivery_order_id']);

        $i = 0;
        foreach ($this->column_search_detail as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }
                if (count($this->column_search_detail) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order_detail[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order_detail)) {
            $order = $this->order_detail;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables_detail()
    {
        $this->_get_datatables_query_detail();
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered_detail()
    {
        $this->_get_datatables_query_detail();
        $query = $this->db->get();
        return $query->num_rows();
    }

    function count_all_detail()
    {
        $this->db->from($this->table_detail);
        $this->db->where('delivery_order_id', $_POST['delivery_order_id']);
        return $this->db->count_all_results();
    }

    function insert_detail($table, $data)
    {
        return $this->db->insert($table, $data);
    }

    function update_detail($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table_detail, $data);
    }

    function get_detail($id)
    {
        $this->db->where('id', $id);
        return $this->db->get($this->table_detail)->row();
    }

    function delete_detail($id, $table)
    {
        $this->db->where('id', $id);
        $this->db->delete($table);
    }

    function get_delivery_order_summary($delivery_order_id)
    {
        $this->db->select('COUNT(*) as total_items, SUM(qty_delivered) as total_qty, SUM(qty_delivered * unit_weight) as total_weight');
        $this->db->from($this->table_detail);
        $this->db->where('delivery_order_id', $delivery_order_id);
        $result = $this->db->get()->row();

        return array(
            'total_items' => $result->total_items ? $result->total_items : 0,
            'total_qty' => $result->total_qty ? $result->total_qty : 0,
            'total_weight' => $result->total_weight ? $result->total_weight : 0
        );
    }

    // Get available orders for delivery
    function get_available_orders()
    {
        $this->db->select('id, order_no, customer_name, order_date, total_amount');
        $this->db->from('orders');
        $this->db->where_in('order_status', array('confirmed', 'processing'));
        $this->db->order_by('order_date', 'desc');
        return $this->db->get()->result();
    }

    // Get order details for delivery
    function get_order_details($order_id)
    {
        $this->db->select('od.*, b.nama as nama_barang, s.nama as nama_satuan');
        $this->db->from('order_detail od');
        $this->db->join('barang b', 'od.id_barang = b.id', 'left');
        $this->db->join('satuan s', 'b.id_satuan = s.id', 'left');
        $this->db->where('od.order_id', $order_id);
        return $this->db->get()->result();
    }

    // Generate delivery number
    function generate_delivery_no()
    {
        $this->db->select('delivery_no');
        $this->db->from($this->table);
        $this->db->like('delivery_no', 'DO-' . date('Y'), 'after');
        $this->db->order_by('id', 'desc');
        $this->db->limit(1);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            $last_no = $query->row()->delivery_no;
            $last_number = (int) substr($last_no, -4);
            $new_number = $last_number + 1;
        } else {
            $new_number = 1;
        }
        
        return 'DO-' . date('Y') . str_pad($new_number, 4, '0', STR_PAD_LEFT);
    }

    // Update delivery order totals
    function update_totals($delivery_order_id)
    {
        $summary = $this->get_delivery_order_summary($delivery_order_id);
        
        $data = array(
            'total_items' => $summary['total_items'],
            'total_qty' => $summary['total_qty'],
            'total_weight' => $summary['total_weight'],
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        $this->db->where('id', $delivery_order_id);
        $this->db->update($this->table, $data);
    }
}
